# Environment variables
.env
.env.*
!.env.example

# Firebase credentials
instance/serviceAccountKey.json
**/serviceAccountKey*.json

# Python files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
logs/
*.egg-info/
.installed.cfg
*.egg

# Project specific files
extract_cities.py
extract_flavors.py
import_coffee.py
update_specific_field.py
dup.py
coffee_data.csv
check_db_schema.py
check_schema.py
database_setup.py
checkenv.py
add_specific_field.py
israeli_coffee_letter.html
israeli_coffee_letter.txt
utils/roasters*.csv
utils/roaster_links.py
edit_price.html
encrypt_password_util.py
coffee_database.db
docs/security_remediation_plan.md
test_roaster_insert.py
create_poll_table.py
scripts/extract_firebase.py
firebase_users.csv
general.db
view_poll_records.py
general.db
check_poll_db.py
scripts/visualize_bean_prices.py
scripts/histogram_bean_prices
.vscode/settings.json
.roomodes

# IDE settings
.vscode/
.idea/
*.swp
*.swo
docs/security_remediation_plan.md

# API management scripts (sensitive)
scripts/debug_api_keys.py
scripts/manage_production_api_keys.py
scripts/debug_production_500.py

EMAIL_MIGRATION_GUIDE.md



