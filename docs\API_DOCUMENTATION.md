# Coffee Beans Search API Documentation

## Overview
The Coffee Beans Search API provides programmatic access to search and filter coffee beans from the Israeli Coffee database. This RESTful API supports both authenticated and unauthenticated access with rate limiting.

## Base URL
```
https://your-domain.com/api
```

## Authentication
The API uses Bearer token authentication via API keys. Include your API key in the Authorization header:

```
Authorization: Bearer your-api-key-here
```

### Getting an API Key
1. Contact the system administrator to request an API key
2. Provide your name and email address
3. You'll receive a unique API key (starts with `coffee_api_`)

## Rate Limiting
- Default rate limit: 60 requests per minute
- Rate limit is tied to your API key
- Exceeding the limit returns HTTP 429 (Too Many Requests)

## Endpoints

### POST /api/search_beans
Search and filter coffee beans based on various criteria.

#### Request Headers
| Header | Type | Required | Description |
|--------|------|----------|-------------|
| Authorization | string | Yes | Bearer token with your API key |
| Content-Type | string | Yes | Must be `application/json` |

#### Request Body
```json
{
  "page": 1,
  "per_page": 20,
  "origin": ["Ethiopia", "Brazil"],
  "processing": "Washed",
  "elevation": ["altd_2"],
  "roast_level": "Medium",
  "flavors": ["chocolate", "fruity"],
  "flavor_mode": "AND",
  "arabica": 1,
  "robusta": 0,
  "singleorigin": 1,
  "mix": 0,
  "speciality": 1,
  "decaf": 0,
  "isra_content": 1,
  "brew_methods": ["espresso", "pour_over"],
  "roasters": ["1", "5"],
  "cities": ["Tel Aviv", "Jerusalem"]
}
```

#### Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | integer | 1 | Page number for pagination (1-based) |
| per_page | integer | 20 | Results per page (max 100) |
| origin | array | [] | Filter by origin countries |
| processing | string | "" | Filter by processing method |
| elevation | array | [] | Filter by elevation ranges: "altd_1" (0-1000m), "altd_2" (1001-1500m), "altd_3" (1501-2000m), "altd_4" (>2000m) |
| roast_level | string | "" | Filter by roast level |
| flavors | array | [] | Filter by flavor notes |
| flavor_mode | string | "OR" | Flavor matching mode: "OR" (any flavor) or "AND" (all flavors) |
| arabica | integer | 0 | Filter for Arabica beans (1 = yes, 0 = no) |
| robusta | integer | 0 | Filter for Robusta beans (1 = yes, 0 = no). When both arabica=1 and robusta=1, returns beans with BOTH types |
| singleorigin | integer | 0 | Filter for single-origin beans |
| mix | integer | 0 | Filter for blend beans |
| speciality | integer | 0 | Filter for speciality beans |
| decaf | integer | 0 | Filter for decaffeinated beans |
| isra_content | integer | 0 | Filter for Israeli content |
| brew_methods | array | [] | Filter by brew methods: "turkish", "espresso", "french_press", "pour_over", "drip", "cold_brew" |
| roasters | array | [] | Filter by roaster IDs |
| cities | array | [] | Filter by roaster cities |

#### Response Format
```json
{
  "data": [
    {
      "bean_id": 123,
      "bean_name": "Ethiopian Yirgacheffe",
      "roaster_id": 45,
      "origin": "Ethiopia",
      "processing": "Washed",
      "elevation": "1800-2200m",
      "body": "Medium",
      "acidity": "High",
      "flavors": "floral, citrus, chocolate",
      "roast_level": "Light",
      "arabica": 100,
      "robusta": 0,
      "singleorigin": 1,
      "mix": 0,
      "price": 89.0,
      "weight": 250,
      "turkish": 1,
      "espresso": 1,
      "french_press": 1,
      "pour_over": 1,
      "drip": 1,
      "cold_brew": 0,
      "speciality": 1,
      "SCA_score": 87.5,
      "roaster_name": "Coffee Roasters Ltd",
      "roaster_webpage": "https://example.com",
      "isra_content": 1
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total": 156,
    "total_pages": 8
  },
  "meta": {
    "api_key_name": "Demo User",
    "rate_limit": 60
  }
}
```

#### Response Fields
**Data Array Fields:**
- `bean_id`: Unique identifier for the bean
- `bean_name`: Name of the coffee bean
- `roaster_id`: ID of the roaster
- `origin`: Country of origin
- `processing`: Processing method (Washed, Natural, Honey, etc.)
- `elevation`: Growing elevation range or specific value
- `body`: Body characteristic (Light, Medium, Full)
- `acidity`: Acidity level (Low, Medium, High)
- `flavors`: Flavor notes and tasting profile (comma-separated string)
- `roast_level`: Light, Medium, or Dark
- `arabica`: Percentage of Arabica (0-100)
- `robusta`: Percentage of Robusta (0-100)
- `singleorigin`: 1 if single-origin, 0 if blend
- `mix`: 1 if blend, 0 if single-origin
- `price`: Price in ILS
- `weight`: Weight in grams
- `turkish`, `espresso`, `french_press`, `pour_over`, `drip`, `cold_brew`: 1 if suitable for this brew method
- `speciality`: 1 if speciality grade
- `SCA_score`: Specialty Coffee Association score (if available)
- `roaster_name`: Name of the roasting company
- `roaster_webpage`: Website URL of the roaster
- `isra_content`: 1 if contains Israeli content

**Pagination Fields:**
- `page`: Current page number
- `per_page`: Results per page
- `total`: Total number of matching beans
- `total_pages`: Total number of pages

**Meta Fields:**
- `api_key_name`: Name associated with your API key
- `rate_limit`: Your current rate limit

### OPTIONS /api/search_beans
Handles CORS preflight requests. Returns empty response with appropriate headers.

## Error Handling

### HTTP Status Codes
| Code | Description |
|------|-------------|
| 200 | Success |
| 400 | Bad Request - Invalid JSON or parameters |
| 401 | Unauthorized - Missing or invalid API key |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error |

### Error Response Format
```json
{
  "error": "Descriptive error message"
}
```

## Examples

### Basic Search
```bash
curl -X POST https://your-domain.com/api/search_beans \
  -H "Authorization: Bearer coffee_api_demo_key_2024" \
  -H "Content-Type: application/json" \
  -d '{"origin": ["Ethiopia"], "speciality": 1}'
```

### Paginated Search
```bash
curl -X POST https://your-domain.com/api/search_beans \
  -H "Authorization: Bearer coffee_api_demo_key_2024" \
  -H "Content-Type: application/json" \
  -d '{"page": 2, "per_page": 10, "roast_level": "Medium"}'
```

### Complex Filter
```bash
curl -X POST https://your-domain.com/api/search_beans \
  -H "Authorization: Bearer coffee_api_demo_key_2024" \
  -H "Content-Type: application/json" \
  -d '{
    "origin": ["Colombia", "Brazil"],
    "processing": "Washed",
    "elevation": ["altd_2", "altd_3"],
    "flavors": ["chocolate", "nutty"],
    "flavor_mode": "AND",
    "arabica": 1,
    "robusta": 1,
    "speciality": 1,
    "brew_methods": ["espresso", "pour_over"]
  }'
```

### Elevation Range Filter
```bash
curl -X POST https://your-domain.com/api/search_beans \
  -H "Authorization: Bearer coffee_api_demo_key_2024" \
  -H "Content-Type: application/json" \
  -d '{
    "elevation": ["altd_3"],
    "speciality": 1
  }'
```

### Flavor AND/OR Logic
```bash
# Find beans with ALL specified flavors (AND logic)
curl -X POST https://your-domain.com/api/search_beans \
  -H "Authorization: Bearer coffee_api_demo_key_2024" \
  -H "Content-Type: application/json" \
  -d '{
    "flavors": ["chocolate", "citrus", "floral"],
    "flavor_mode": "AND"
  }'

# Find beans with ANY of the specified flavors (OR logic - default)
curl -X POST https://your-domain.com/api/search_beans \
  -H "Authorization: Bearer coffee_api_demo_key_2024" \
  -H "Content-Type: application/json" \
  -d '{
    "flavors": ["chocolate", "citrus", "floral"],
    "flavor_mode": "OR"
  }'
```

### Arabica/Robusta Blend Filter
```bash
# Find beans that contain BOTH Arabica AND Robusta (blends)
curl -X POST https://your-domain.com/api/search_beans \
  -H "Authorization: Bearer coffee_api_demo_key_2024" \
  -H "Content-Type: application/json" \
  -d '{
    "arabica": 1,
    "robusta": 1
  }'
```

## JavaScript Example
```javascript
async function searchCoffeeBeans() {
  const response = await fetch('/api/search_beans', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer your-api-key',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      origin: ['Ethiopia'],
      elevation: ['altd_3'], // High elevation (1501-2000m)
      flavors: ['floral', 'citrus'],
      flavor_mode: 'OR', // Any of the flavors
      speciality: 1,
      arabica: 1,
      page: 1,
      per_page: 10
    })
  });

  const data = await response.json();
  console.log(`Found ${data.pagination.total} beans`);
  data.data.forEach(bean => {
    console.log(`${bean.bean_name} - ${bean.origin} - ₪${bean.price} - ${bean.arabica}% Arabica`);
  });
}
```

## Python Example
```python
import requests

url = "https://your-domain.com/api/search_beans"
headers = {
    "Authorization": "Bearer coffee_api_demo_key_2024",
    "Content-Type": "application/json"
}

payload = {
    "origin": ["Ethiopia", "Kenya"],
    "elevation": ["altd_2", "altd_3"],  # Medium to high elevation
    "flavors": ["chocolate", "berry", "wine"],
    "flavor_mode": "OR",  # Any of these flavors
    "arabica": 1,
    "speciality": 1,
    "page": 1,
    "per_page": 20
}

response = requests.post(url, json=payload, headers=headers)
data = response.json()

print(f"Found {data['pagination']['total']} beans")
for bean in data['data']:
    print(f"{bean['bean_name']} - {bean['origin']} - ₪{bean['price']} - {bean['arabica']}% Arabica - {bean['flavors']}")
```

## Special Parameters

### Elevation Ranges
The `elevation` parameter accepts specific range codes:
- `"altd_1"`: 0-1000 meters (Low elevation)
- `"altd_2"`: 1001-1500 meters (Medium elevation)  
- `"altd_3"`: 1501-2000 meters (High elevation)
- `"altd_4"`: Above 2000 meters (Very high elevation)

### Flavor Matching Modes
The `flavor_mode` parameter controls how multiple flavors are matched:
- `"OR"` (default): Returns beans that contain ANY of the specified flavors
- `"AND"`: Returns beans that contain ALL of the specified flavors

### Arabica/Robusta Logic
- `arabica: 1, robusta: 0`: Returns beans with Arabica content (percentage > 0)
- `arabica: 0, robusta: 1`: Returns beans with Robusta content (percentage > 0)  
- `arabica: 1, robusta: 1`: Returns beans with BOTH Arabica AND Robusta (blends only)
- `arabica: 0, robusta: 0`: No filter applied

## Rate Limiting Best Practices
- Cache responses when possible
- Implement exponential backoff for retries
- Monitor your usage with the `X-RateLimit-*` headers (when implemented)
- Consider using webhooks for real-time updates instead of polling

## Support
For API support, questions, or to request additional features, please contact:
- Email: <EMAIL>
- API Status Page: https://status.israeli-coffee.com