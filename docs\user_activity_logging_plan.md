# User Activity Logging Implementation Plan

## 1. Overall Goal

Implement a system to log user activities in the Flask application, including user ID (Firebase UID or 'anonymous'), action performed, timestamp, and session duration upon logout. Logs will be stored in JSON format, with each entry on a new line.

## 2. Approved Initial Scope (Based on User Feedback)

*   **Log File Path:** For initial development, logs will be written to a local file: `logs/activity.log` (relative to the project root). This can be configured for production (e.g., `/var/log/myapp/activity.log`) later.
*   **Session Duration:** Session duration will be logged primarily when a user explicitly logs out.
*   **Specific Actions Logged:** Initially, the focus will be on logging page visits (e.g., "visited /dashboard") and explicit logout events.
*   **Unauthenticated Users:** For actions performed by users not logged in, the `user_id` will be logged as "anonymous".

## 3. Proposed Implementation Phases

### Phase 1: Logging Setup and Basic Activity Logging

#### Step 1.1: Create a Logging Utility

*   **File:** Create a new file `utils/log_utils.py`.
*   **Function:** Define a function `setup_activity_logger(log_file_path)` within this file.
    *   This function will configure Python's built-in `logging` module.
    *   It will create a logger named `activity_logger`.
    *   It will add a `logging.FileHandler` to write to the `log_file_path`.
    *   It will use a custom `logging.Formatter` (or a helper class/method) to ensure log messages are in JSON format. Each log entry should be a separate JSON object on a new line.
    *   Example JSON structure for a general activity:
        ```json
        {
          "timestamp": "YYYY-MM-DDTHH:MM:SS.ffffffZ",
          "user_id": "firebase_uid_or_anonymous",
          "action": "visited /dashboard",
          "method": "GET",
          "path": "/dashboard",
          "status_code": 200,
          "ip_address": "*******",
          "user_agent": "Mozilla/5.0..."
        }
        ```
*   **Initialization:** In `app.py`, import `setup_activity_logger` and call it early during application initialization (e.g., after `app = Flask(__name__)`).

#### Step 1.2: Log User Actions (Page Visits)

*   **Decorator:** In `app.py`, use an `@app.after_request` decorator.
*   **Handler Function:**
    *   This function will receive the `response` object.
    *   Extract `user_id`:
        *   Attempt to get it from `g.user.uid`.
        *   Fall back to `session.get('uid', 'anonymous')`.
    *   `action_description = f"visited {request.path}"`
    *   Gather other details: `request.method`, `request.path`, `response.status_code`, `request.remote_addr`, `request.user_agent.string`.
    *   `timestamp = datetime.utcnow().isoformat()` (ensure `from datetime import datetime` is present).
    *   Use the configured `activity_logger` to log the JSON payload.

### Phase 2: Session Duration Logging

#### Step 2.1: Track Session Start Time

*   **Login:** In the `set_token` function in `app.py` (around line 232), after successfully verifying the token and setting `session['id_token']` and `session['uid']`, add:
    `session['session_start_time'] = time.time()` (ensure `import time` is present).
*   **Existing Session:** In the `load_logged_in_user` function in `app.py` (around line 112), if `g.user` is successfully loaded and `session.get('session_start_time')` is `None`, set `session['session_start_time'] = time.time()`.

#### Step 2.2: Log Session Duration on Logout

*   **Logout Function:** Modify the `logout` function in `app.py` (around line 251).
*   **Before `session.clear()`:**
    *   Retrieve `user_id = session.get('uid', 'anonymous')`.
    *   Get `start_time = session.get('session_start_time')`.
    *   If `start_time` exists:
        *   `duration_seconds = time.time() - start_time`
        *   Log an event:
            ```json
            {
              "timestamp": "YYYY-MM-DDTHH:MM:SS.ffffffZ",
              "user_id": "firebase_uid_or_anonymous",
              "action": "logout",
              "session_duration_seconds": 1234.56,
              "ip_address": "*******",
              "user_agent": "Mozilla/5.0..."
            }
            ```
    *   Then proceed with `session.clear()`.

### Phase 3: Configuration and Refinements

#### Step 3.1: Log File Path Configuration

*   Use an environment variable (e.g., `ACTIVITY_LOG_PATH`) to specify the log file location.
*   Provide a default value in `app.py` or `utils/log_utils.py` (e.g., `logs/activity.log` relative to the project root) for development if the environment variable is not set. Ensure the `logs/` directory can be created or exists.

#### Step 3.2: Error Handling

*   Wrap logging calls in `try...except` blocks to ensure that logging failures do not crash the application. Log any such errors to the standard Flask logger or `stderr`.

## 4. Mermaid Diagram of Logging Flow

```mermaid
graph TD
    subgraph User Interaction
        A[User Request] --> B{Flask App};
    end

    subgraph Flask App Processing
        B -- @app.before_request --> C[load_logged_in_user];
        C -- Sets g.user --> D;
        C -- Sets session_start_time (if needed) --> D;
        D[Route Handler];
        D -- @app.after_request --> E[Activity Logging Handler];
        F[set_token on Login] -- Sets session_start_time --> B;
        G[logout Route] -- Calculates duration & Logs --> H[Clear Session];
    end

    subgraph Logging System
        E -- Formats & Writes Log --> I[(activity.log)];
        G -- Formats & Writes Log --> I;
    end

    style I fill:#f9f,stroke:#333,stroke-width:2px
```

## 5. Next Steps

With this plan approved, the next step is to switch to a development mode (e.g., "Code" mode) to implement these changes.