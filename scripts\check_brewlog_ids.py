import sqlite3
import os
from pathlib import Path

# Get the project root directory
project_root = str(Path(__file__).parent.parent)
DATABASE = os.path.join(project_root, 'coffee_database.db')

def check_brewlog_ids():
    """Check the IDs in the brewlog table to ensure they are correctly formed"""
    print(f"Checking brewlog IDs in: {DATABASE}")
    
    try:
        conn = sqlite3.connect(DATABASE)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # First check the schema
        cursor.execute("PRAGMA table_info(brewlog)")
        columns = cursor.fetchall()
        
        print("Table Schema:")
        for col in columns:
            print(f"  {col['name']}: type={col['type']}, notnull={col['notnull']}, pk={col['pk']}")
        
        # Check for primary key
        pk_cols = [col['name'] for col in columns if col['pk'] == 1]
        if not pk_cols:
            print("\nWARNING: No primary key column found in brewlog table!")
        else:
            print(f"\nPrimary key column: {pk_cols[0]}")
        
        # Check if there's an 'id' column and if it's a primary key
        id_cols = [col for col in columns if col['name'] == 'id']
        if not id_cols:
            print("\nWARNING: No 'id' column found!")
        else:
            id_col = id_cols[0]
            if id_col['pk'] != 1:
                print("\nWARNING: 'id' column exists but is not a primary key!")
        
        # Check if we have any records
        cursor.execute("SELECT COUNT(*) as count FROM brewlog")
        count = cursor.fetchone()['count']
        print(f"\nTotal records in brewlog: {count}")
        
        # If we have records, check some sample IDs
        if count > 0:
            # Use rowid to ensure we can always access records
            cursor.execute("SELECT rowid, * FROM brewlog LIMIT 10")
            rows = cursor.fetchall()
            
            print("\nSample Records:")
            for row in rows:
                id_value = row['id'] if 'id' in row.keys() else "NO ID COLUMN"
                rowid_value = row['rowid']
                uid_value = row['uid'] if 'uid' in row.keys() else "NO UID COLUMN"
                
                print(f"  rowid={rowid_value}, id={id_value}, uid={uid_value}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")
        if 'conn' in locals():
            conn.close()

def fix_id_column():
    """Fix the ID column if it's missing or not a primary key"""
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        
        # Check if an 'id' column exists and if it's a primary key
        cursor.execute("PRAGMA table_info(brewlog)")
        columns = cursor.fetchall()
        
        id_cols = [col for col in columns if col['name'] == 'id']
        has_id = len(id_cols) > 0
        is_id_pk = has_id and id_cols[0]['pk'] == 1
        
        if has_id and is_id_pk:
            print("ID column exists and is a primary key. No action needed.")
            conn.close()
            return
        
        print("Creating backup of brewlog table...")
        cursor.execute("CREATE TABLE IF NOT EXISTS brewlog_backup AS SELECT * FROM brewlog")
        
        print("Creating new table with correct ID column...")
        cursor.execute("""
        CREATE TABLE brewlog_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            uid TEXT NOT NULL,
            bean_id INTEGER NOT NULL,
            brew_date TEXT,
            bean_purchase_date TEXT,
            bean_roasting_date TEXT,
            brew_method TEXT,
            grind_settings REAL,
            brew_temp REAL,
            PreInfusionTimeSec INTEGER,
            brew_time INTEGER,
            coffee_dose REAL,
            coffee_output REAL,
            aroma TEXT,
            acidity TEXT,
            sweetness TEXT,
            bitterness TEXT,
            body TEXT,
            aftertaste TEXT,
            overall_rating INTEGER,
            FOREIGN KEY (bean_id) REFERENCES beans (bean_id)
        )
        """)
        
        # Copy data from old table - exclude id column if it exists
        columns_to_copy = [col['name'] for col in columns if col['name'] != 'id']
        columns_str = ", ".join(columns_to_copy)
        
        print(f"Columns to copy: {columns_str}")
        cursor.execute(f"""
        INSERT INTO brewlog_new ({columns_str})
        SELECT {columns_str} FROM brewlog
        """)
        
        # Replace old table with new one
        cursor.execute("DROP TABLE brewlog")
        cursor.execute("ALTER TABLE brewlog_new RENAME TO brewlog")
        
        print("Table structure fixed. Verifying...")
        
        # Check the new table
        cursor.execute("PRAGMA table_info(brewlog)")
        new_columns = cursor.fetchall()
        
        id_cols = [col for col in new_columns if col['name'] == 'id']
        has_id = len(id_cols) > 0
        is_id_pk = has_id and id_cols[0]['pk'] == 1
        
        if has_id and is_id_pk:
            print("Success! ID column is now a primary key.")
        else:
            print("WARNING: Fix failed. ID column is still not a proper primary key.")
        
        # Count records
        cursor.execute("SELECT COUNT(*) as count FROM brewlog")
        count = cursor.fetchone()[0]
        print(f"Records in new table: {count}")
        
        conn.commit()
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    check_brewlog_ids()
    
    answer = input("\nDo you want to try to fix the ID column? (y/n): ")
    if answer.lower() == 'y':
        fix_id_column()
