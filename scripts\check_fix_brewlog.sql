-- SQL script to check and potentially fix the uid field in the brewlog table

-- Check the brewlog table structure
PRAGMA table_info(brewlog);

-- Check existing records in brewlog
SELECT id, uid, bean_id, brew_date, brew_method
FROM brewlog
LIMIT 10;

-- If needed: Modify table to ensure uid is TEXT
-- Uncomment and run only if needed:
-- CREATE TABLE brewlog_new (
--     id INTEGER PRIMARY KEY AUTOINCREMENT,
--     uid TEXT NOT NULL,
--     bean_id INTEGER NOT NULL,
--     brew_date TEXT,
--     bean_purchase_date TEXT,
--     bean_roasting_date TEXT,
--     brew_method TEXT,
--     grind_settings REAL,
--     brew_temp REAL,
--     PreInfusionTimeSec INTEGER,
--     brew_time INTEGER,
--     coffee_dose REAL,
--     coffee_output REAL,
--     aroma TEXT,
--     acidity TEXT,
--     sweetness TEXT,
--     bitterness TEXT,
--     body TEXT,
--     aftertaste TEXT,
--     overall_rating INTEGER,
--     FOREIGN KEY (bean_id) REFERENCES beans (bean_id)
-- );

-- If needed: Copy the existing data to new table with explicit TEXT conversion for uid
-- Uncomment and run only if needed:
-- INSERT INTO brewlog_new 
-- SELECT id, CAST(uid AS TEXT), bean_id, brew_date, bean_purchase_date, bean_roasting_date, 
--        brew_method, grind_settings, brew_temp, PreInfusionTimeSec, brew_time, coffee_dose, 
--        coffee_output, aroma, acidity, sweetness, bitterness, body, aftertaste, overall_rating
-- FROM brewlog;

-- Rename tables to replace the old one with the new one if needed
-- Uncomment and run only if needed:
-- DROP TABLE brewlog;
-- ALTER TABLE brewlog_new RENAME TO brewlog;
