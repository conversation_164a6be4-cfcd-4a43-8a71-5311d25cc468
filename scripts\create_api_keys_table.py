#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the api_keys table in the existing coffee_database.db
This table will store API keys for external API access to the search functionality.
"""

import sqlite3
import os
import hashlib
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_api_keys_table():
    """Create the api_keys table in the coffee_database.db"""
    
    # Get the database path from environment variable or use default
    db_path = os.environ.get('DATABASE_PATH', 'instance/coffee_database.db')
    
    # For production, try common locations if default doesn't exist
    if not os.path.exists(db_path):
        possible_paths = [
            'coffee_database.db',
            '/var/www/coffee/instance/coffee_database.db',
            '/opt/coffee/instance/coffee_database.db',
            './instance/coffee_database.db'
        ]
        for path in possible_paths:
            if os.path.exists(path):
                db_path = path
                break
    
    print(f"Using database at: {db_path}")
    
    # Ensure the instance directory exists
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create the api_keys table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS api_keys (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key_hash TEXT NOT NULL UNIQUE,
            name TEXT NOT NULL,
            email TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_used TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            rate_limit_per_minute INTEGER DEFAULT 60,
            total_requests INTEGER DEFAULT 0,
            expires_at TIMESTAMP
        )
    ''')
    
    # Create indexes for performance
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_api_keys_key_hash ON api_keys(key_hash)
    ''')
    
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_api_keys_active ON api_keys(is_active)
    ''')
    
    # Create a sample API key for testing (only if table is empty)
    cursor.execute('SELECT COUNT(*) FROM api_keys')
    if cursor.fetchone()[0] == 0:
        # Generate a sample API key
        sample_key = "coffee_api_demo_key_2024"
        key_hash = hashlib.sha256(sample_key.encode()).hexdigest()
        
        # Insert sample key that expires in 30 days
        expires_at = datetime.now() + timedelta(days=30)
        cursor.execute('''
            INSERT INTO api_keys (key_hash, name, email, is_active, rate_limit_per_minute, expires_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (key_hash, 'Israelicoffee', '<EMAIL>', True, 100, expires_at))
        
        print(f"Sample API key created: {sample_key}")
        print("IMPORTANT: Store this key securely - it won't be shown again!")
    
    conn.commit()
    conn.close()
    
    print(f"API keys table created successfully in {db_path}")
    print("Table schema:")
    print("- id: Primary key")
    print("- key_hash: SHA256 hash of the API key")
    print("- name: Descriptive name for the key")
    print("- email: Contact email for the key owner")
    print("- created_at: When the key was created")
    print("- last_used: Last time the key was used")
    print("- is_active: Whether the key is active")
    print("- rate_limit_per_minute: Requests per minute limit")
    print("- total_requests: Total requests made with this key")
    print("- expires_at: When the key expires (NULL for no expiration)")

def generate_api_key(name, email=None, expires_days=None, rate_limit=60):
    """Generate a new API key and store it in the database"""
    
    import secrets
    
    # Generate a secure random API key
    api_key = f"coffee_api_{secrets.token_urlsafe(32)}"
    key_hash = hashlib.sha256(api_key.encode()).hexdigest()
    
    db_path = os.environ.get('DATABASE_PATH', 'instance/coffee_database.db')
    
    # Check if database exists
    if not os.path.exists(db_path):
        # For production, try common locations if default doesn't exist
        possible_paths = [
            'coffee_database.db',
            '/var/www/coffee/instance/coffee_database.db',
            '/opt/coffee/instance/coffee_database.db',
            './instance/coffee_database.db'
        ]
        for path in possible_paths:
            if os.path.exists(path):
                db_path = path
                break
        else:
            raise FileNotFoundError(f"Database not found at {db_path}. Run create_api_keys_table() first.")
    
    print(f"Using database at: {db_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Calculate expiration date
        expires_at = None
        if expires_days:
            expires_at = datetime.now() + timedelta(days=expires_days)
        
        # Insert the new key
        cursor.execute('''
            INSERT INTO api_keys (key_hash, name, email, rate_limit_per_minute, expires_at)
            VALUES (?, ?, ?, ?, ?)
        ''', (key_hash, name, email, rate_limit, expires_at))
        
        conn.commit()
        
        # Verify the key was inserted
        cursor.execute('SELECT COUNT(*) FROM api_keys WHERE key_hash = ?', (key_hash,))
        if cursor.fetchone()[0] != 1:
            raise Exception("Failed to insert API key into database")
            
        return api_key
        
    except Exception as e:
        conn.rollback()
        raise Exception(f"Database error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    import sys
    
    # Check if we should generate a new API key
    if len(sys.argv) > 1:
        if sys.argv[1] == "generate":
            # Create the table first if it doesn't exist
            try:
                create_api_keys_table()
            except Exception as e:
                print(f"⚠️  Warning: Could not create/verify table: {e}")
            
            # Get parameters from command line arguments
            name = sys.argv[2] if len(sys.argv) > 2 else "Production API Key"
            email = sys.argv[3] if len(sys.argv) > 3 else None
            expires_days = int(sys.argv[4]) if len(sys.argv) > 4 else 365  # Default 1 year
            rate_limit = int(sys.argv[5]) if len(sys.argv) > 5 else 100  # Default 100 req/min
            
            # Generate the new key
            try:
                new_key = generate_api_key(name, email, expires_days, rate_limit)
                print(f"\n✅ SUCCESS: New API key generated!")
                print(f"🔑 API Key: {new_key}")
                print(f"📝 Name: {name}")
                print(f"📧 Email: {email or 'Not provided'}")
                print(f"⚡ Rate Limit: {rate_limit} requests/minute")
                print(f"⏰ Expires: {expires_days} days from now")
                print(f"\n🔒 IMPORTANT: Store this key securely - it won't be shown again!")
                print(f"📋 Use in Authorization header: Bearer {new_key}")
                print(f"\n🧪 Test your API key with:")
                print(f"curl -H \"Authorization: Bearer {new_key}\" -H \"Content-Type: application/json\" \\")
                print(f"     -d '{{\"page\":1,\"per_page\":5}}' \\")
                print(f"     http://your-domain.com/api/search_beans")
            except Exception as e:
                print(f"❌ ERROR generating API key: {e}")
                sys.exit(1)
        elif sys.argv[1] in ["-h", "--help", "help"]:
            print("🔑 Coffee API Key Management")
            print("=" * 50)
            print("Usage:")
            print("  python create_api_keys_table.py                    # Create table only")
            print("  python create_api_keys_table.py generate [name] [email] [expires_days] [rate_limit]")
            print("\nExamples:")
            print("  # Create table only")
            print("  python create_api_keys_table.py")
            print("\n  # Generate production API key")
            print("  python create_api_keys_table.py generate 'Production API' '<EMAIL>' 365 100")
            print("\n  # Generate test API key with 30 day expiration")
            print("  python create_api_keys_table.py generate 'Test Key' '<EMAIL>' 30 50")
            print("\nParameters:")
            print("  name         - Descriptive name for the API key")
            print("  email        - Contact email (optional)")
            print("  expires_days - Number of days until expiration (default: 365)")
            print("  rate_limit   - Requests per minute limit (default: 100)")
        else:
            print("❌ Unknown command. Use 'help' for usage information.")
            sys.exit(1)
    else:
        # Just create the table
        try:
            create_api_keys_table()
            print("\n✅ Table created successfully!")
            print("\n🔑 To generate a new API key, run:")
            print("python create_api_keys_table.py generate 'Your Key Name' '<EMAIL>' 365 100")
            print("\n💡 For help, run:")
            print("python create_api_keys_table.py help")
        except Exception as e:
            print(f"❌ ERROR creating table: {e}")
            sys.exit(1)