import sqlite3
import os

DATABASE_FILE = 'coffee_database.db'
SCRIPT_DIR = os.path.dirname(__file__)
PROJECT_ROOT = os.path.abspath(os.path.join(SCRIPT_DIR, '..'))
DB_PATH = os.path.join(PROJECT_ROOT, DATABASE_FILE)

def create_deals_table():
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = ON;")
        print("Foreign key constraints enabled.")

        # SQL to create the deals table
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS deals (
            deal_id INTEGER PRIMARY KEY AUTOINCREMENT,
            roaster_id INTEGER,
            deal_date DATE DEFAULT CURRENT_DATE,
            deal_text TEXT,
            deal_code TEXT,
            FOREIGN KEY (roaster_id) REFERENCES roasters(roaster_id)
        );
        """
        cursor.execute(create_table_sql)
        conn.commit()
        print(f"Table 'deals' created successfully in {DATABASE_FILE}.")

        # Verify table creation
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='deals';")
        if cursor.fetchone():
            print("Verification successful: 'deals' table exists.")
        else:
            print("Verification failed: 'deals' table does not exist.")

    except sqlite3.Error as e:
        print(f"An error occurred: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()
            print("Database connection closed.")
 
def create_new_contest_file():
     conn = None
     try:
         conn = sqlite3.connect(DB_PATH)
         cursor = conn.cursor()
 
         # SQL to add 'isra_content' column to the 'beans' table
         alter_table_sql = """
         ALTER TABLE beans
         ADD COLUMN isra_content BOOLEAN DEFAULT FALSE;
         """
         cursor.execute(alter_table_sql)
         conn.commit()
         print("Column 'isra_content' added to 'beans' table successfully.")
 
     except sqlite3.Error as e:
         print(f"An error occurred: {e}")
         if conn:
             conn.rollback()
     finally:
         if conn:
             conn.close()
             print("Database connection closed.")
 
if __name__ == "__main__":
     create_deals_table()
     create_new_contest_file()