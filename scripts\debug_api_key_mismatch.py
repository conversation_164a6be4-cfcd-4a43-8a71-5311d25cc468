#!/usr/bin/env python3
"""
Debug API Key Mismatch - Compare .env key with database
"""

import sqlite3
import os
import hashlib

def load_env_file():
    """Load .env file"""
    env_path = '.env'
    if os.path.exists(env_path):
        print(f"📄 Loading .env file from: {env_path}")
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    # Remove quotes if present
                    value = value.strip('"\'')
                    os.environ[key] = value
        print("✅ .env file loaded")
    else:
        print("❌ .env file not found")

def debug_api_key():
    print("🔍 API Key Debug Tool")
    print("=" * 50)
    
    # Load environment
    load_env_file()
    
    # Get API key from environment
    api_key = os.environ.get('COFFEE_API_KEY')
    
    if not api_key:
        print("❌ COFFEE_API_KEY not found in environment")
        return
    
    print(f"🔑 Environment API Key: '{api_key}'")
    print(f"📏 Length: {len(api_key)} characters")
    print(f"🔤 First 20 chars: '{api_key[:20]}'")
    print(f"🔤 Last 20 chars: '{api_key[-20:]}'")
    
    # Check for hidden characters
    print(f"🔍 Raw bytes: {api_key.encode('utf-8')}")
    
    # Generate hash
    key_hash = hashlib.sha256(api_key.encode()).hexdigest()
    print(f"🔐 SHA256 Hash: {key_hash}")
    
    # Find database
    db_paths = ['coffee_database.db', 'instance/coffee_database.db']
    db_path = None
    
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("❌ Database not found")
        return
    
    print(f"\n📁 Using database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if api_keys table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='api_keys'")
        if not cursor.fetchone():
            print("❌ api_keys table does not exist!")
            return
        
        # Get all API keys from database
        cursor.execute('SELECT id, name, key_hash, is_active FROM api_keys')
        keys_in_db = cursor.fetchall()
        
        print(f"\n📋 API Keys in Database ({len(keys_in_db)}):")
        print("-" * 50)
        
        for key_record in keys_in_db:
            key_id, name, db_hash, is_active = key_record
            status = "🟢 ACTIVE" if is_active else "🔴 INACTIVE"
            
            print(f"[{key_id}] {name} {status}")
            print(f"    DB Hash: {db_hash}")
            
            # Compare hashes
            if db_hash == key_hash:
                print(f"    ✅ HASH MATCHES your .env key!")
            else:
                print(f"    ❌ Hash does NOT match")
            print()
        
        # Look for exact hash match
        cursor.execute('SELECT id, name, is_active FROM api_keys WHERE key_hash = ?', (key_hash,))
        matching_key = cursor.fetchone()
        
        print("\n🎯 Authentication Check:")
        if matching_key:
            key_id, name, is_active = matching_key
            print(f"✅ Found matching key: [{key_id}] {name}")
            if is_active:
                print("✅ Key is ACTIVE")
                print("🤔 Key should work! Check these potential issues:")
                print("   1. Make sure Flask app is using the same database")
                print("   2. Check if API endpoint is reading .env correctly")
                print("   3. Verify no caching issues")
                print("   4. Check request format (Authorization: Bearer <key>)")
            else:
                print("❌ Key is INACTIVE - this will cause 401 errors!")
        else:
            print("❌ NO MATCHING KEY FOUND - this will cause 401 errors!")
            print("💡 Solutions:")
            print("   1. Generate a new API key")
            print("   2. Check for typos in .env file")
            print("   3. Verify database is correct")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Database error: {e}")

if __name__ == "__main__":
    debug_api_key()
