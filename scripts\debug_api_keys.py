#!/usr/bin/env python3
"""
SECURITY WARNING: This script shows actual API keys. Use only for setup/debugging.
Delete this script after use in production.
"""

import sqlite3
import os
import hashlib

def show_demo_key():
    """Show the demo API key for comparison"""
    
    demo_key = "coffee_api_demo_key_2024"
    demo_hash = hashlib.sha256(demo_key.encode()).hexdigest()
    
    print(f"Demo Key: {demo_key}")
    print(f"Demo Hash: {demo_hash}")
    
    # Check if this matches what's in database
    db_path = os.environ.get('DATABASE_PATH', 'instance/coffee_database.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id, name, key_hash FROM api_keys WHERE key_hash = ?', (demo_hash,))
        result = cursor.fetchone()
        
        if result:
            print(f"✅ Found matching key in database: ID={result[0]}, Name={result[1]}")
            print(f"\n🔧 To use this key, update your .env file:")
            print(f"COFFEE_API_KEY=\"{demo_key}\"")
        else:
            print("❌ Demo key hash not found in database")
            
            # Show all hashes for debugging
            cursor.execute('SELECT id, name, key_hash FROM api_keys')
            all_keys = cursor.fetchall()
            print(f"\nDatabase contains {len(all_keys)} key(s):")
            for key in all_keys:
                print(f"  ID={key[0]}, Name={key[1]}, Hash={key[2][:16]}...")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ ERROR: {e}")

def generate_new_production_key():
    """Generate a new production key"""
    print("\n" + "="*50)
    print("🚀 GENERATING NEW PRODUCTION KEY")
    print("="*50)
    
    import secrets
    
    # Generate a secure API key
    api_key = f"coffee_api_{secrets.token_urlsafe(32)}"
    key_hash = hashlib.sha256(api_key.encode()).hexdigest()
    
    db_path = os.environ.get('DATABASE_PATH', 'instance/coffee_database.db')
    
    try:
        from datetime import datetime, timedelta
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Insert new key
        expires_at = datetime.now() + timedelta(days=365)  # 1 year
        cursor.execute('''
            INSERT INTO api_keys (key_hash, name, email, rate_limit_per_minute, expires_at, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (key_hash, 'Production API Key', '<EMAIL>', 100, expires_at, True))
        
        conn.commit()
        
        # Get the new key ID
        cursor.execute('SELECT id FROM api_keys WHERE key_hash = ?', (key_hash,))
        new_id = cursor.fetchone()[0]
        
        print(f"✅ NEW PRODUCTION KEY CREATED!")
        print(f"🆔 Database ID: {new_id}")
        print(f"🔑 API Key: {api_key}")
        print(f"⏰ Expires: {expires_at.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⚡ Rate Limit: 100 req/min")
        print(f"\n🔧 Update your .env file with:")
        print(f"COFFEE_API_KEY=\"{api_key}\"")
        
        conn.close()
        return api_key
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return None

if __name__ == "__main__":
    print("🔐 API Key Debug Tool")
    print("=" * 50)
    
    # First show demo key info
    show_demo_key()
    
    # Ask user if they want to generate new key
    response = input("\n🤔 Generate a new production API key? (y/N): ").lower()
    if response in ['y', 'yes']:
        new_key = generate_new_production_key()
        if new_key:
            print(f"\n💡 Next steps:")
            print(f"1. Update .env: COFFEE_API_KEY=\"{new_key}\"")
            print(f"2. Restart your Flask application")
            print(f"3. Delete this debug script for security")
    else:
        print("\n✅ No new key generated. Use existing demo key if it works.")
