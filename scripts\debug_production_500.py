#!/usr/bin/env python3
"""
Production Debug Script for API Endpoint Issues
Run this on your VPS to diagnose the 500 Internal Server Error
"""

import sqlite3
import os
import hashlib
import json
from datetime import datetime

# Load .env file if it exists
def load_env_file():
    """Load .env file if it exists"""
    env_paths = ['.env', '../.env', '/var/www/israeli.coffee/.env']
    
    for env_path in env_paths:
        if os.path.exists(env_path):
            print(f"📄 Loading .env file from: {env_path}")
            try:
                with open(env_path, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            # Remove quotes if present
                            value = value.strip('"\'')
                            os.environ[key] = value
                print(f"✅ .env file loaded successfully")
                return True
            except Exception as e:
                print(f"⚠️  Error loading .env file: {e}")
                return False
    
    print("⚠️  No .env file found in common locations")
    return False

def check_database():
    """Check if database exists and is accessible"""
    print("🔍 Checking Database...")
    print("=" * 40)
    
    # Possible database paths (production focused)
    paths = [
        os.environ.get('DATABASE_PATH'),
        'coffee_database.db'
    ]
    # Filter out None values
    paths = [p for p in paths if p is not None]
    
    db_path = None
    for path in paths:
        if os.path.exists(path):
            db_path = path
            print(f"✅ Database found: {db_path}")
            break
        else:
            print(f"⚠️  Checked: {path} (not found)")
    
    if not db_path:
        print("❌ Database not found! This is likely the main issue.")
        print("💡 Make sure the database path is correct for your production environment")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"📋 Available tables: {tables}")
        
        required_tables = ['beans', 'roasters', 'api_keys']
        missing_tables = [table for table in required_tables if table not in tables]
        
        if missing_tables:
            print(f"❌ Missing tables: {missing_tables}")
            print("💡 Your database might be corrupted or incomplete")
            return False
        
        print(f"✅ Required tables exist: {required_tables}")
        
        # Check data counts
        try:
            cursor.execute("SELECT COUNT(*) FROM beans")
            bean_count = cursor.fetchone()[0]
            print(f"📊 Beans in database: {bean_count}")
        except Exception as e:
            print(f"⚠️  Error counting beans: {e}")
        
        try:
            cursor.execute("SELECT COUNT(*) FROM roasters")
            roaster_count = cursor.fetchone()[0]
            print(f"📊 Roasters in database: {roaster_count}")
        except Exception as e:
            print(f"⚠️  Error counting roasters: {e}")
        
        try:
            cursor.execute("SELECT COUNT(*) FROM api_keys WHERE is_active = 1")
            active_keys = cursor.fetchone()[0]
            print(f"🔑 Active API keys: {active_keys}")
            
            if active_keys == 0:
                print("❌ No active API keys found! This will cause 500 errors.")
                return False
        except Exception as e:
            print(f"⚠️  Error checking API keys: {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        print("💡 Database might be locked, corrupted, or have permission issues")
        return False

def check_api_key_validation():
    """Check API key validation"""
    print("\n🔑 Checking API Key Validation...")
    print("=" * 40)
    
    # Get API key from environment
    api_key = os.environ.get('COFFEE_API_KEY')
    
    if not api_key:
        print("❌ COFFEE_API_KEY not found in environment")
        return False
    
    print(f"✅ Environment API key: {api_key[:20]}...")
    
    # Check if this key exists in database
    try:
        db_path = None
        paths = [
            os.environ.get('DATABASE_PATH', 'coffee_database.db')
        ]
        
        for path in paths:
            if os.path.exists(path):
                db_path = path
                break
        
        if not db_path:
            print("❌ Database not found for API key validation")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        cursor.execute('''
            SELECT id, name, is_active, expires_at, rate_limit_per_minute
            FROM api_keys 
            WHERE key_hash = ?
        ''', (key_hash,))
        
        result = cursor.fetchone()
        
        if not result:
            print("❌ API key not found in database")
            print(f"Key hash: {key_hash}")
            
            # Show existing keys
            cursor.execute('SELECT id, name, key_hash FROM api_keys')
            existing = cursor.fetchall()
            print(f"Existing keys in database: {len(existing)}")
            for key in existing:
                print(f"  ID: {key[0]}, Name: {key[1]}, Hash: {key[2][:16]}...")
            
            return False
        
        key_id, name, is_active, expires_at, rate_limit = result
        
        print(f"✅ API key found in database")
        print(f"  ID: {key_id}")
        print(f"  Name: {name}")
        print(f"  Active: {'Yes' if is_active else 'No'}")
        print(f"  Rate Limit: {rate_limit} req/min")
        print(f"  Expires: {expires_at or 'Never'}")
        
        # Check if expired
        if expires_at:
            try:
                expires_dt = datetime.fromisoformat(expires_at)
                if datetime.now() > expires_dt:
                    print("❌ API key has EXPIRED!")
                    return False
            except:
                pass
        
        if not is_active:
            print("❌ API key is INACTIVE!")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ API key validation error: {e}")
        return False

def test_flask_app():
    """Test if Flask app can be imported (optional check)"""
    print("\n🧪 Testing Flask App Import...")
    print("=" * 40)
    
    try:
        import sys
        sys.path.append('.')
        
        # Try to import the Flask app (non-critical)
        try:
            from app import app
            print("✅ Flask app imported successfully")
            return True
        except ImportError as e:
            print(f"⚠️  Flask app import failed (might be OK): {e}")
            print("   This could be due to missing dependencies or different environment")
            return True  # Don't fail on import issues
        except Exception as e:
            print(f"⚠️  Flask app import error: {e}")
            return True  # Don't fail on other issues
        
    except Exception as e:
        print(f"⚠️  Flask test failed: {e}")
        return True  # Make this non-critical

def check_environment():
    """Check environment variables"""
    print("\n🌍 Checking Environment...")
    print("=" * 40)
    
    # First try to load .env file
    load_env_file()
    
    required_vars = ['COFFEE_API_KEY']
    optional_vars = ['SECRET_KEY', 'FLASK_ENV', 'DATABASE_PATH']
    
    missing_required = []
    
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var}: {value[:20]}...")
        else:
            print(f"❌ {var}: Not set")
            missing_required.append(var)
    
    for var in optional_vars:
        value = os.environ.get(var)
        if value:
            if var == 'SECRET_KEY':
                print(f"✅ {var}: {'*' * min(len(value), 10)}")
            else:
                print(f"✅ {var}: {value}")
        else:
            print(f"⚠️  {var}: Not set (optional)")
    
    return len(missing_required) == 0

def check_file_permissions():
    """Check file permissions and accessibility"""
    print("\n📁 Checking File Permissions...")
    print("=" * 40)
    
    # Check if we can read the current directory
    try:
        files = os.listdir('.')
        print(f"✅ Can read current directory: {len(files)} files")
    except Exception as e:
        print(f"❌ Cannot read current directory: {e}")
        return False
    
    # Check if app.py exists
    if os.path.exists('app.py'):
        try:
            with open('app.py', 'r') as f:
                content = f.read(100)  # Read first 100 chars
            print("✅ Can read app.py")
        except Exception as e:
            print(f"❌ Cannot read app.py: {e}")
            return False
    else:
        print("⚠️  app.py not found in current directory")
    
    return True

def main():
    print("🚨 Production API Debug Tool")
    print("=" * 50)
    print(f"Timestamp: {datetime.now()}")
    print(f"Working Directory: {os.getcwd()}")
    print(f"Python Path: {os.environ.get('PYTHONPATH', 'Not set')}")
    
    # Run all checks
    checks = [
        ("Environment Variables", check_environment),
        ("File Permissions", check_file_permissions),
        ("Database", check_database),
        ("API Key Validation", check_api_key_validation),
        ("Flask App Import", test_flask_app),
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        try:
            results[check_name] = check_func()
        except Exception as e:
            print(f"\n❌ {check_name} check failed with exception: {e}")
            results[check_name] = False
    
    # Summary
    print("\n📋 Summary")
    print("=" * 50)
    
    all_passed = True
    for check_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {check_name}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 All checks passed! The 500 error might be due to:")
        print("  - Ad blocker blocking the request (ERR_BLOCKED_BY_CLIENT)")
        print("  - CORS issues in your web server configuration")
        print("  - Flask application not running or misconfigured")
        print("  - Server load/timeout issues")
        print("  - Check your web server logs (Apache/Nginx) and Flask application logs")
    else:
        print(f"\n🚨 Some checks failed. These are likely causing your 500 errors:")
        failed_checks = [name for name, passed in results.items() if not passed]
        for check in failed_checks:
            print(f"  - Fix: {check}")
    
    print(f"\n💡 Next debugging steps:")
    print("  1. Fix any failing checks above")
    print("  2. Check web server logs: sudo tail -f /var/log/apache2/error.log")
    print("  3. Check Flask app logs if you have them configured")
    print("  4. Test API manually with curl:")
    print(f"     curl -X POST https://israeli.coffee/api/search_beans \\")
    print(f"          -H 'Authorization: Bearer {os.environ.get('COFFEE_API_KEY', 'YOUR_API_KEY')}' \\")
    print(f"          -H 'Content-Type: application/json' \\")
    print(f"          -d '{{\"page\": 1, \"per_page\": 5}}'")
    print("  5. If curl works but browser doesn't, it's likely an ad blocker issue")

if __name__ == "__main__":
    main()
