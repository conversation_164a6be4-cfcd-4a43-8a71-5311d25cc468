#!/usr/bin/env python3
"""
Debug template rendering to see what API key is being passed
"""

import requests
import re
import os
from dotenv import load_dotenv

def debug_template_rendering():
    print("🔍 Debugging Template Rendering")
    print("=" * 50)
    
    # Load environment
    load_dotenv()
    expected_api_key = os.environ.get('COFFEE_API_KEY')
    print(f"🔑 Expected API key from .env: {expected_api_key}")
    
    try:
        # Create a session to maintain cookies and referrer
        session = requests.Session()
        
        # First, visit the home page to establish referrer
        home_response = session.get("http://127.0.0.1:5000/", timeout=10)
        
        # Now visit search_beans with proper referrer
        headers = {
            'Referer': 'http://127.0.0.1:5000/'
        }
        response = session.get("http://127.0.0.1:5000/search_beans", headers=headers, timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to fetch search_beans page: {response.status_code}")
            return
        
        html_content = response.text
        
        # Look for the exact meta tag line
        lines = html_content.split('\n')
        for i, line in enumerate(lines):
            if 'api-key' in line:
                print(f"📄 Line {i+1}: {line.strip()}")
                
        # Also search for any occurrence of our API key
        if expected_api_key in html_content:
            print(f"✅ Found expected API key in HTML content")
        else:
            print(f"❌ Expected API key NOT found in HTML content")
            
        # Search for demo key
        if 'demo-key-12345-67890-abcdef' in html_content:
            print(f"⚠️  Found demo key in HTML content")
            # Find where it appears
            demo_lines = []
            for i, line in enumerate(lines):
                if 'demo-key-12345-67890-abcdef' in line:
                    demo_lines.append((i+1, line.strip()))
            
            print(f"Demo key found on {len(demo_lines)} lines:")
            for line_num, line_content in demo_lines[:3]:  # Show first 3
                print(f"  Line {line_num}: {line_content[:100]}...")
                
        # Check if the meta tag exists but is empty
        empty_meta_pattern = r'<meta name="api-key" content="">'
        if re.search(empty_meta_pattern, html_content):
            print("🚨 Found EMPTY api-key meta tag!")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    debug_template_rendering()
