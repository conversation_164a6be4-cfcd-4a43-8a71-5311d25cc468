#!/usr/bin/env python3
"""
Debug what API key the web interface is actually getting
"""

import requests
import re
import os
from dotenv import load_dotenv

def debug_web_api_key():
    print("🔍 Debugging Web Interface API Key")
    print("=" * 50)
    
    # Load environment
    load_dotenv()
    expected_api_key = os.environ.get('COFFEE_API_KEY')
    print(f"🔑 Expected API key from .env: {expected_api_key[:20]}...")
    
    try:
        # Fetch the search_beans page
        response = requests.get("http://127.0.0.1:5000/search_beans", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to fetch search_beans page: {response.status_code}")
            return
        
        html_content = response.text
        
        # Extract API key from meta tag
        meta_pattern = r'<meta name="api-key" content="([^"]+)">'
        match = re.search(meta_pattern, html_content)
        
        if match:
            web_api_key = match.group(1)
            print(f"🌐 API key in web page: {web_api_key[:20]}...")
            
            if web_api_key == expected_api_key:
                print("✅ Web page API key MATCHES .env file")
            else:
                print("❌ Web page API key DOES NOT MATCH .env file!")
                print(f"   Expected: {expected_api_key}")
                print(f"   Got:      {web_api_key}")
                
                if web_api_key == 'demo-key-12345-67890-abcdef':
                    print("🚨 Web page is using DEMO key - this will cause 401 errors!")
        else:
            print("❌ Could not find api-key meta tag in HTML")
            
        # Also check if there are any JavaScript console errors we can detect
        if 'demo-key-12345-67890-abcdef' in html_content:
            print("⚠️  Found demo key hardcoded in HTML/JavaScript")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - make sure Flask app is running on http://127.0.0.1:5000")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    debug_web_api_key()
