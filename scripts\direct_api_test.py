#!/usr/bin/env python3
"""
Direct API Key Test - Bypass Firebase issues
"""

import sqlite3
import hashlib
import os
from datetime import datetime

def load_env_file():
    """Load .env file from parent directory"""
    env_path = '.env'
    if os.path.exists(env_path):
        print(f"📄 Loading .env file from: {env_path}")
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    value = value.strip('"\'')
                    os.environ[key] = value
        print("✅ .env file loaded")
        return True
    return False

def direct_api_test():
    print("🔍 Direct API Key Database Test")
    print("=" * 50)
    
    # Load environment
    if not load_env_file():
        print("❌ Could not load .env file")
        return
    
    # Get API key
    api_key = os.environ.get('COFFEE_API_KEY')
    if not api_key:
        print("❌ COFFEE_API_KEY not found in environment")
        return
    
    print(f"🔑 API Key: {api_key}")
    
    # Hash the key
    key_hash = hashlib.sha256(api_key.encode()).hexdigest()
    print(f"🔐 Key Hash: {key_hash}")
    
    # Connect to database
    db_path = 'coffee_database.db'
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # This is important - matches Flask's setup
        cursor = conn.cursor()
        
        # Query like the Flask function does
        cursor.execute('''
            SELECT id, name, email, rate_limit_per_minute, total_requests, expires_at, is_active
            FROM api_keys
            WHERE key_hash = ? AND is_active = 1
        ''', (key_hash,))
        
        key_record = cursor.fetchone()
        
        if not key_record:
            print("❌ No valid API key found in database!")
            print("This explains the 401 errors!")
            
            # Let's see what's actually in the database
            cursor.execute('SELECT id, name, key_hash, is_active FROM api_keys')
            all_keys = cursor.fetchall()
            print(f"\nAll keys in database ({len(all_keys)}):")
            for key in all_keys:
                print(f"  ID: {key['id']}, Name: {key['name']}, Active: {key['is_active']}")
                print(f"  Hash: {key['key_hash']}")
                print(f"  Matches: {'YES' if key['key_hash'] == key_hash else 'NO'}")
                print()
        else:
            print("✅ API Key found in database!")
            print(f"  ID: {key_record['id']}")
            print(f"  Name: {key_record['name']}")
            print(f"  Email: {key_record['email']}")
            print(f"  Active: {key_record['is_active']}")
            
            # Check expiration
            if key_record['expires_at']:
                expires_at = datetime.fromisoformat(key_record['expires_at'])
                if datetime.now() > expires_at:
                    print("❌ KEY HAS EXPIRED!")
                else:
                    print("✅ Key is not expired")
            else:
                print("✅ Key never expires")
            
            print("\n🎉 API Key validation should work!")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Database error: {e}")

if __name__ == "__main__":
    direct_api_test()
