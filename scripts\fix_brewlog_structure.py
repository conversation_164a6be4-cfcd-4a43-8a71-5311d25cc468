import sqlite3
import os
import sys

# Add the parent directory to the path so we can use the same database path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

DATABASE = 'coffee_database.db'

def fix_brewlog_table():
    """Fix the brewlog table structure to ensure it has a proper primary key."""
    try:
        conn = sqlite3.connect(DATABASE)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Check the current table structure
        cursor.execute("PRAGMA table_info(brewlog)")
        columns = cursor.fetchall()
        
        # Check if there's already an 'id' column with PRIMARY KEY
        has_proper_id = False
        for col in columns:
            if col['name'] == 'id' and col['pk'] == 1:
                has_proper_id = True
                break
        
        if has_proper_id:
            print("The brewlog table already has a proper 'id' column as PRIMARY KEY.")
            conn.close()
            return True
            
        print("Fixing the brewlog table structure...")
        
        # Create a backup first
        cursor.execute("CREATE TABLE IF NOT EXISTS brewlog_backup AS SELECT * FROM brewlog")
        print("Created a backup table: brewlog_backup")
        
        # Now fix the main table
        cursor.execute("""
            CREATE TABLE brewlog_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                uid TEXT NOT NULL,
                bean_id INTEGER NOT NULL,
                brew_date TEXT,
                bean_purchase_date TEXT,
                bean_roasting_date TEXT,
                brew_method TEXT,
                grind_settings REAL,
                brew_temp REAL,
                PreInfusionTimeSec INTEGER,
                brew_time INTEGER,
                coffee_dose REAL,
                coffee_output REAL,
                aroma TEXT,
                acidity TEXT,
                sweetness TEXT,
                bitterness TEXT,
                body TEXT,
                aftertaste TEXT,
                overall_rating INTEGER,
                FOREIGN KEY (bean_id) REFERENCES beans (bean_id)
            )
        """)
        
        # Copy existing data
        cursor.execute("""
            INSERT INTO brewlog_new (
                uid, bean_id, brew_date, bean_purchase_date, bean_roasting_date,
                brew_method, grind_settings, brew_temp, PreInfusionTimeSec,
                brew_time, coffee_dose, coffee_output, aroma, acidity,
                sweetness, bitterness, body, aftertaste, overall_rating
            )
            SELECT
                uid, bean_id, brew_date, bean_purchase_date, bean_roasting_date,
                brew_method, grind_settings, brew_temp, PreInfusionTimeSec,
                brew_time, coffee_dose, coffee_output, aroma, acidity,
                sweetness, bitterness, body, aftertaste, overall_rating
            FROM brewlog
        """)
        
        # Replace the table
        cursor.execute("DROP TABLE brewlog")
        cursor.execute("ALTER TABLE brewlog_new RENAME TO brewlog")
        
        # Verify the structure
        cursor.execute("PRAGMA table_info(brewlog)")
        new_columns = cursor.fetchall()
        print("New table structure:")
        for col in new_columns:
            print(f"{col['name']}: {col['type']}, PK: {col['pk']}")
            
        # Check record count
        cursor.execute("SELECT COUNT(*) as count FROM brewlog")
        record_count = cursor.fetchone()['count']
        print(f"Transferred {record_count} records to the new table.")
        
        conn.commit()
        conn.close()
        print("Table structure fixed successfully!")
        return True
        
    except Exception as e:
        print(f"Error fixing brewlog table: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    fix_brewlog_table()
