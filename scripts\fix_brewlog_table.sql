-- <PERSON><PERSON>t to fix the brewlog table structure if it doesn't have a proper primary key
-- Run this script to ensure the brewlog table has an 'id' column as PRIMARY KEY AUTOINCREMENT

-- Check the current table structure
PRAGMA table_info(brewlog);

-- Create a backup of the current table just in case
CREATE TABLE brewlog_backup AS SELECT * FROM brewlog;

-- Create a new brewlog table with proper id column
CREATE TABLE brewlog_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uid TEXT NOT NULL,
    bean_id INTEGER NOT NULL,
    brew_date TEXT,
    bean_purchase_date TEXT,
    bean_roasting_date TEXT,
    brew_method TEXT,
    grind_settings REAL,
    brew_temp REAL,
    PreInfusionTimeSec INTEGER,
    brew_time INTEGER,
    coffee_dose REAL,
    coffee_output REAL,
    aroma TEXT,
    acidity TEXT,
    sweetness TEXT,
    bitterness TEXT,
    body TEXT,
    aftertaste TEXT,
    overall_rating INTEGER,
    FOREIGN KEY (bean_id) REFERENCES beans (bean_id)
);

-- Copy data from the old table to the new table
INSERT INTO brewlog_new (
    uid, bean_id, brew_date, bean_purchase_date, bean_roasting_date,
    brew_method, grind_settings, brew_temp, PreInfusionTimeSec,
    brew_time, coffee_dose, coffee_output, aroma, acidity,
    sweetness, bitterness, body, aftertaste, overall_rating
)
SELECT
    uid, bean_id, brew_date, bean_purchase_date, bean_roasting_date,
    brew_method, grind_settings, brew_temp, PreInfusionTimeSec,
    brew_time, coffee_dose, coffee_output, aroma, acidity,
    sweetness, bitterness, body, aftertaste, overall_rating
FROM brewlog;

-- Drop the old table and rename the new one
DROP TABLE brewlog;
ALTER TABLE brewlog_new RENAME TO brewlog;

-- Verify the new table structure
PRAGMA table_info(brewlog);

-- Check that data was transferred properly
SELECT COUNT(*) FROM brewlog;
