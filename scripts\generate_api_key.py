#!/usr/bin/env python3
"""
API Key Generator for Coffee Beans Search API

This script generates new API keys and stores them in the database.
Usage: python generate_api_key.py --name "User Name" --email "<EMAIL>" [--expires-days 365] [--rate-limit 60]
"""

import sqlite3
import hashlib
import secrets
import argparse
from datetime import datetime, timedelta
import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def generate_api_key():
    """Generate a secure API key"""
    prefix = "coffee_api_"
    random_part = secrets.token_urlsafe(32)
    return f"{prefix}{random_part}"

def create_api_key(name, email, expires_days=None, rate_limit=60):
    """Create a new API key and store it in the database"""
    
    # Generate the API key
    api_key = generate_api_key()
    key_hash = hashlib.sha256(api_key.encode()).hexdigest()
    
    # Connect to database
    db_path = os.environ.get('DATABASE_PATH', 'instance/coffee_database.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Calculate expiration date
        expires_at = None
        if expires_days:
            expires_at = (datetime.now() + timedelta(days=expires_days)).isoformat()
        
        # Insert the API key
        cursor.execute('''
            INSERT INTO api_keys (key_hash, name, email, rate_limit_per_minute, expires_at, is_active, created_at)
            VALUES (?, ?, ?, ?, ?, 1, CURRENT_TIMESTAMP)
        ''', (key_hash, name, email, rate_limit, expires_at))
        
        conn.commit()
        
        # Get the inserted ID
        api_key_id = cursor.lastrowid
        
        print(f"✅ API Key Generated Successfully!")
        print(f"🔑 API Key: {api_key}")
        print(f"📧 Email: {email}")
        print(f"👤 Name: {name}")
        print(f"⏱️  Rate Limit: {rate_limit} requests/minute")
        if expires_days:
            print(f"📅 Expires: {expires_at}")
        else:
            print(f"📅 Expires: Never")
        print(f"🆔 Key ID: {api_key_id}")
        print(f"\n⚠️  Save this key securely - it won't be shown again!")
        
        return api_key
        
    except sqlite3.IntegrityError as e:
        print(f"❌ Error: {e}")
        return None
    finally:
        conn.close()

def list_api_keys():
    """List all active API keys (without showing the actual keys)"""
    db_path = os.environ.get('DATABASE_PATH', 'instance/coffee_database.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT id, name, email, rate_limit_per_minute, total_requests, 
                   created_at, last_used, expires_at, is_active
            FROM api_keys
            WHERE is_active = 1
            ORDER BY created_at DESC
        ''')
        
        keys = cursor.fetchall()
        
        if not keys:
            print("No active API keys found.")
            return
        
        print("\n📋 Active API Keys:")
        print("-" * 80)
        for key in keys:
            print(f"ID: {key[0]}")
            print(f"Name: {key[1]}")
            print(f"Email: {key[2]}")
            print(f"Rate Limit: {key[3]} req/min")
            print(f"Total Requests: {key[4]}")
            print(f"Created: {key[5]}")
            print(f"Last Used: {key[6] or 'Never'}")
            print(f"Expires: {key[7] or 'Never'}")
            print(f"Active: {'Yes' if key[8] else 'No'}")
            print("-" * 80)
            
    finally:
        conn.close()

def revoke_api_key(key_id):
    """Revoke an API key by ID"""
    db_path = os.path.join(os.path.dirname(__file__), '..', 'instance', 'coffee.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        cursor.execute('UPDATE api_keys SET is_active = 0 WHERE id = ?', (key_id,))
        conn.commit()
        
        if cursor.rowcount > 0:
            print(f"✅ API Key ID {key_id} has been revoked.")
        else:
            print(f"❌ API Key ID {key_id} not found.")
            
    finally:
        conn.close()

def main():
    parser = argparse.ArgumentParser(description='Generate API keys for Coffee Beans Search API')
    parser.add_argument('--name', required=True, help='Name of the API key owner')
    parser.add_argument('--email', required=True, help='Email of the API key owner')
    parser.add_argument('--expires-days', type=int, help='Number of days until expiration (optional)')
    parser.add_argument('--rate-limit', type=int, default=60, help='Rate limit in requests per minute (default: 60)')
    parser.add_argument('--list', action='store_true', help='List all active API keys')
    parser.add_argument('--revoke', type=int, help='Revoke API key by ID')
    
    args = parser.parse_args()
    
    if args.list:
        list_api_keys()
    elif args.revoke:
        revoke_api_key(args.revoke)
    else:
        create_api_key(args.name, args.email, args.expires_days, args.rate_limit)

if __name__ == "__main__":
    main()