#!/usr/bin/env python3
"""
Simple script to generate a production API key for the coffee search API.
Usage: python generate_production_api_key.py
"""

import sqlite3
import os
import hashlib
import secrets
from datetime import datetime, <PERSON><PERSON><PERSON>

def generate_production_api_key():
    """Generate a production API key"""
    
    # Configuration for production
    KEY_NAME = "Israeli Coffee Production API"
    EMAIL = "<EMAIL>"
    EXPIRES_DAYS = 365  # 1 year
    RATE_LIMIT = 100    # 100 requests per minute
    
    # Get database path
    db_path = os.environ.get('DATABASE_PATH', 'instance/coffee_database.db')
    
    # Alternative paths to check
    possible_paths = [
        'instance/coffee_database.db',
        'coffee_database.db',
        '../instance/coffee_database.db',
        '../coffee_database.db'
    ]
    
    # Find the database
    if not os.path.exists(db_path):
        for path in possible_paths:
            if os.path.exists(path):
                db_path = path
                break
        else:
            print(f"❌ ERROR: Database not found. Checked:")
            for path in [db_path] + possible_paths:
                print(f"  - {os.path.abspath(path)}")
            return None
    
    print(f"📁 Using database: {os.path.abspath(db_path)}")
    
    try:
        # Generate API key
        api_key = f"coffee_api_{secrets.token_urlsafe(32)}"
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if api_keys table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='api_keys'")
        if not cursor.fetchone():
            print("❌ ERROR: api_keys table does not exist. Run create_api_keys_table.py first.")
            return None
        
        # Calculate expiration
        expires_at = datetime.now() + timedelta(days=EXPIRES_DAYS)
        
        # Insert the key
        cursor.execute('''
            INSERT INTO api_keys (key_hash, name, email, rate_limit_per_minute, expires_at, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (key_hash, KEY_NAME, EMAIL, RATE_LIMIT, expires_at, True))
        
        conn.commit()
        
        # Verify insertion
        cursor.execute('SELECT id FROM api_keys WHERE key_hash = ?', (key_hash,))
        key_id = cursor.fetchone()
        
        if key_id:
            print(f"\n✅ SUCCESS: Production API key generated!")
            print(f"🔑 API Key: {api_key}")
            print(f"📋 Name: {KEY_NAME}")
            print(f"📧 Email: {EMAIL}")
            print(f"⚡ Rate Limit: {RATE_LIMIT} requests/minute")
            print(f"⏰ Expires: {expires_at.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"🆔 Database ID: {key_id[0]}")
            print(f"\n🔒 IMPORTANT: Store this key securely!")
            print(f"📋 Authorization Header: Bearer {api_key}")
            print(f"\n🧪 Test with curl:")
            print(f'curl -X POST http://localhost:5000/api/search_beans \\')
            print(f'  -H "Authorization: Bearer {api_key}" \\')
            print(f'  -H "Content-Type: application/json" \\')
            print(f'  -d "{{\\"page\\": 1, \\"per_page\\": 5}}"')
            
            return api_key
        else:
            print("❌ ERROR: Failed to verify API key insertion")
            return None
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return None
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("🚀 Generating Production API Key for Israeli Coffee...")
    print("=" * 50)
    
    api_key = generate_production_api_key()
    
    if api_key:
        print("\n" + "=" * 50)
        print("✅ API Key generation completed successfully!")
    else:
        print("\n" + "=" * 50)
        print("❌ API Key generation failed!")
        exit(1)
