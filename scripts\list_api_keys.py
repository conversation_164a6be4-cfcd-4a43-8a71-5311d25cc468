#!/usr/bin/env python3
"""
Quick script to list all API keys in the database
"""

import sqlite3
import os
from datetime import datetime

def list_api_keys():
    """List all API keys in the database"""
    
    db_path = os.environ.get('DATABASE_PATH', 'instance/coffee_database.db')
    
    # Alternative paths to check
    possible_paths = [
        'instance/coffee_database.db',
        'coffee_database.db',
        '../instance/coffee_database.db',
        '../coffee_database.db'
    ]
    
    # Find the database
    if not os.path.exists(db_path):
        for path in possible_paths:
            if os.path.exists(path):
                db_path = path
                break
        else:
            print(f"❌ ERROR: Database not found. Checked:")
            for path in [db_path] + possible_paths:
                print(f"  - {os.path.abspath(path)}")
            return
    
    print(f"📁 Using database: {os.path.abspath(db_path)}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if api_keys table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='api_keys'")
        if not cursor.fetchone():
            print("❌ ERROR: api_keys table does not exist.")
            return
        
        # Get all API keys
        cursor.execute('''
            SELECT id, name, email, created_at, last_used, is_active, 
                   rate_limit_per_minute, total_requests, expires_at
            FROM api_keys
            ORDER BY created_at DESC
        ''')
        
        keys = cursor.fetchall()
        
        if not keys:
            print("📝 No API keys found in database.")
            print("\n💡 To create a new API key, run:")
            print("python scripts/create_api_keys_table.py generate 'Production API' '<EMAIL>' 365 100")
        else:
            print(f"\n📋 Found {len(keys)} API key(s):")
            print("=" * 80)
            for key in keys:
                id, name, email, created_at, last_used, is_active, rate_limit, total_requests, expires_at = key
                
                status = "🟢 ACTIVE" if is_active else "🔴 INACTIVE"
                
                print(f"ID: {id}")
                print(f"Name: {name}")
                print(f"Email: {email or 'Not provided'}")
                print(f"Status: {status}")
                print(f"Rate Limit: {rate_limit} req/min")
                print(f"Total Requests: {total_requests}")
                print(f"Created: {created_at}")
                print(f"Last Used: {last_used or 'Never'}")
                print(f"Expires: {expires_at or 'Never'}")
                
                # Check if expired
                if expires_at:
                    expires_dt = datetime.fromisoformat(expires_at)
                    if datetime.now() > expires_dt:
                        print("⚠️  WARNING: This key has EXPIRED!")
                
                print("-" * 40)
        
        conn.close()
        
    except Exception as e:
        print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    print("🔑 Listing API Keys in Database...")
    print("=" * 50)
    list_api_keys()
