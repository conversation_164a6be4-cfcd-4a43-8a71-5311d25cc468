#!/usr/bin/env python3
"""
Production API Key Management Script
Use this script on your VPS to manage API keys in production.
"""

import sqlite3
import os
import hashlib
import secrets
from datetime import datetime, timedelta

class APIKeyManager:
    def __init__(self):
        # Production database paths to check
        self.possible_paths = [        
            'coffee_database.db'
        ]
        self.db_path = self.find_database()
    
    def find_database(self):
        """Find the production database"""
        # Check environment variable first
        db_path = os.environ.get('DATABASE_PATH')
        if db_path and os.path.exists(db_path):
            return db_path
        
        # Check common production paths
        for path in self.possible_paths:
            if os.path.exists(path):
                return path
        
        print("❌ ERROR: Database not found. Checked:")
        for path in self.possible_paths:
            print(f"  - {os.path.abspath(path)}")
        return None
    
    def ensure_api_keys_table(self):
        """Create api_keys table if it doesn't exist"""
        if not self.db_path:
            return False
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='api_keys'")
            if cursor.fetchone():
                print("✅ api_keys table exists")
                conn.close()
                return True
            
            print("🔧 Creating api_keys table...")
            
            # Create the api_keys table
            cursor.execute('''
                CREATE TABLE api_keys (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key_hash TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    email TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    last_used DATETIME,
                    is_active BOOLEAN DEFAULT 1,
                    rate_limit_per_minute INTEGER DEFAULT 60,
                    total_requests INTEGER DEFAULT 0,
                    expires_at DATETIME
                )
            ''')
            
            # Create index for faster lookups
            cursor.execute('CREATE INDEX idx_api_keys_hash ON api_keys(key_hash)')
            cursor.execute('CREATE INDEX idx_api_keys_active ON api_keys(is_active)')
            
            conn.commit()
            conn.close()
            
            print("✅ api_keys table created successfully")
            return True
            
        except Exception as e:
            print(f"❌ ERROR creating api_keys table: {e}")
            return False
    
    def list_keys(self):
        """List all API keys"""
        if not self.db_path:
            return
        
        # Ensure the api_keys table exists
        if not self.ensure_api_keys_table():
            return []
        
        print(f"📁 Using database: {os.path.abspath(self.db_path)}")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get all keys
            cursor.execute('''
                SELECT id, name, email, created_at, last_used, is_active, 
                       rate_limit_per_minute, total_requests, expires_at
                FROM api_keys
                ORDER BY created_at DESC
            ''')
            
            keys = cursor.fetchall()
            
            if not keys:
                print("📝 No API keys found in database.")
                return []
            
            print(f"\n📋 Found {len(keys)} API key(s):")
            print("=" * 80)
            
            for key in keys:
                id, name, email, created_at, last_used, is_active, rate_limit, total_requests, expires_at = key
                
                status = "🟢 ACTIVE" if is_active else "🔴 INACTIVE"
                
                print(f"[{id}] {name}")
                print(f"    Email: {email or 'Not provided'}")
                print(f"    Status: {status}")
                print(f"    Rate Limit: {rate_limit} req/min")
                print(f"    Total Requests: {total_requests}")
                print(f"    Created: {created_at}")
                print(f"    Last Used: {last_used or 'Never'}")
                print(f"    Expires: {expires_at or 'Never'}")
                
                # Check if expired
                if expires_at:
                    try:
                        expires_dt = datetime.fromisoformat(expires_at)
                        if datetime.now() > expires_dt:
                            print("    ⚠️  WARNING: EXPIRED!")
                    except:
                        pass
                
                print()
            
            conn.close()
            return keys
            
        except Exception as e:
            print(f"❌ ERROR: {e}")
            return []
    
    def delete_key(self, key_id):
        """Delete an API key by ID"""
        if not self.db_path:
            return False
        
        # Ensure the api_keys table exists
        if not self.ensure_api_keys_table():
            return False
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if key exists
            cursor.execute('SELECT name FROM api_keys WHERE id = ?', (key_id,))
            result = cursor.fetchone()
            
            if not result:
                print(f"❌ ERROR: API key with ID {key_id} not found.")
                return False
            
            key_name = result[0]
            
            # Delete the key
            cursor.execute('DELETE FROM api_keys WHERE id = ?', (key_id,))
            conn.commit()
            
            if cursor.rowcount > 0:
                print(f"✅ Successfully deleted API key: {key_name} (ID: {key_id})")
                return True
            else:
                print(f"❌ Failed to delete API key with ID {key_id}")
                return False
            
        except Exception as e:
            print(f"❌ ERROR deleting key: {e}")
            return False
        finally:
            if 'conn' in locals():
                conn.close()
    
    def generate_key(self, name="Production API Key", email="<EMAIL>", expires_days=365, rate_limit=100):
        """Generate a new API key"""
        if not self.db_path:
            return None
        
        # Ensure the api_keys table exists
        if not self.ensure_api_keys_table():
            return None
        
        try:
            # Generate secure API key
            api_key = f"coffee_api_{secrets.token_urlsafe(32)}"
            key_hash = hashlib.sha256(api_key.encode()).hexdigest()
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Calculate expiration
            expires_at = datetime.now() + timedelta(days=expires_days)
            
            # Insert new key
            cursor.execute('''
                INSERT INTO api_keys (key_hash, name, email, rate_limit_per_minute, expires_at, is_active)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (key_hash, name, email, rate_limit, expires_at, True))
            
            conn.commit()
            
            # Get the new key ID
            cursor.execute('SELECT id FROM api_keys WHERE key_hash = ?', (key_hash,))
            new_id = cursor.fetchone()[0]
            
            print(f"✅ NEW API KEY CREATED!")
            print(f"🆔 Database ID: {new_id}")
            print(f"📋 Name: {name}")
            print(f"📧 Email: {email}")
            print(f"⚡ Rate Limit: {rate_limit} req/min")
            print(f"⏰ Expires: {expires_at.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"\n🔑 API Key: {api_key}")
            print(f"\n🔧 Update your .env file with:")
            print(f'COFFEE_API_KEY="{api_key}"')
            
            conn.close()
            return api_key
            
        except Exception as e:
            print(f"❌ ERROR generating key: {e}")
            return None

def main():
    print("🔑 Production API Key Manager")
    print("=" * 50)
    
    manager = APIKeyManager()
    
    while True:
        print("\nOptions:")
        print("1. List all API keys")
        print("2. Delete an API key")
        print("3. Generate new API key")
        print("4. Clean up and create new (delete all + create)")
        print("5. Exit")
        
        choice = input("\nSelect option (1-5): ").strip()
        
        if choice == "1":
            manager.list_keys()
        
        elif choice == "2":
            keys = manager.list_keys()
            if keys:
                try:
                    key_id = int(input("\nEnter API key ID to delete: ").strip())
                    confirm = input(f"Are you sure you want to delete key ID {key_id}? (y/N): ").lower()
                    if confirm in ['y', 'yes']:
                        manager.delete_key(key_id)
                except ValueError:
                    print("❌ Invalid ID. Please enter a number.")
        
        elif choice == "3":
            name = input("Enter key name (default: Production API Key): ").strip() or "Production API Key"
            email = input("Enter email (default: <EMAIL>): ").strip() or "<EMAIL>"
            
            try:
                expires_days = input("Enter expiration days (default: 365): ").strip()
                expires_days = int(expires_days) if expires_days else 365
                
                rate_limit = input("Enter rate limit per minute (default: 100): ").strip()
                rate_limit = int(rate_limit) if rate_limit else 100
            except ValueError:
                print("❌ Invalid number. Using defaults.")
                expires_days = 365
                rate_limit = 100
            
            manager.generate_key(name, email, expires_days, rate_limit)
        
        elif choice == "4":
            print("\n⚠️  DANGER: This will delete ALL existing API keys and create a new one!")
            confirm = input("Are you absolutely sure? Type 'DELETE ALL' to confirm: ")
            
            if confirm == "DELETE ALL":
                # Get all keys and delete them
                keys = manager.list_keys()
                deleted_count = 0
                
                for key in keys:
                    key_id = key[0]  # ID is the first field
                    if manager.delete_key(key_id):
                        deleted_count += 1
                
                print(f"\n🗑️  Deleted {deleted_count} API key(s)")
                
                # Create new key
                print("\n🆕 Creating new production API key...")
                manager.generate_key("Israeli Coffee Production API", "<EMAIL>", 365, 100)
            else:
                print("❌ Operation cancelled.")
        
        elif choice == "5":
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid option. Please select 1-5.")

if __name__ == "__main__":
    main()
