#!/usr/bin/env python3
"""
Test the actual API endpoint with our API key
"""

import requests
import json
import os
from dotenv import load_dotenv

def test_api_endpoint():
    print("🧪 Testing API Endpoint Directly")
    print("=" * 50)
    
    # Load environment
    load_dotenv()
    api_key = os.environ.get('COFFEE_API_KEY')
    
    if not api_key:
        print("❌ No API key found")
        return
    
    print(f"🔑 Using API key: {api_key[:20]}...")
    
    # Test data
    test_data = {
        "page": 1,
        "per_page": 5
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}'
    }
    
    try:
        # Test local endpoint
        url = 'http://localhost:5000/api/search_beans'
        print(f"🌐 Testing: {url}")
        
        response = requests.post(url, 
                               headers=headers, 
                               json=test_data,
                               timeout=10)
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API call successful!")
            print(f"📈 Results: {len(data.get('results', []))} beans")
            print(f"📄 Total: {data.get('total', 0)} total results")
        elif response.status_code == 401:
            print("❌ 401 Unauthorized - API key authentication failed!")
            print(f"Response: {response.text}")
        else:
            print(f"❌ Error {response.status_code}: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection refused - Flask app is not running")
        print("💡 Start Flask with: python app.py")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_api_endpoint()
