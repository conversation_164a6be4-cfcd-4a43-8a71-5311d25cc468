#!/usr/bin/env python3
"""
Test API Key Validation Function Directly
"""

import os
import sys

# Load .env file
def load_env_file():
    env_path = '.env'
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    value = value.strip('"\'')
                    os.environ[key] = value

def test_api_validation():
    print("🧪 Testing API Key Validation Function")
    print("=" * 50)
    
    # Load environment
    load_env_file()
    
    # Get API key
    api_key = os.environ.get('COFFEE_API_KEY')
    print(f"🔑 API Key: {api_key}")
    
    try:
        # Import Flask app
        sys.path.append('..')
        sys.path.append('.')
        from app import validate_api_key
        
        # Test validation
        result = validate_api_key(api_key)
        
        if result:
            print("✅ API Key Validation: SUCCESS")
            print(f"📋 Key Info: {result}")
        else:
            print("❌ API Key Validation: FAILED")
            print("This explains the 401 errors!")
            
    except Exception as e:
        print(f"❌ Error testing validation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_api_validation()
