import sqlite3
import sys
import os

# Add the parent directory to the path so we can import app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

DATABASE = 'coffee_database.db'

def get_db_connection():
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def test_fetch_brew_records(uid=None):
    """Test fetching brew records from the database."""
    print("=== TESTING BREW RECORD FETCHING ===")
    
    if uid is None:
        print("No UID provided, will show all records")
    else:
        print(f"Testing with UID: '{uid}', Type: {type(uid)}")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get total count
        cursor.execute("SELECT COUNT(*) as count FROM brewlog")
        total_count = cursor.fetchone()['count']
        print(f"Total brewlog records: {total_count}")
        
        # Get distinct UIDs
        cursor.execute("SELECT DISTINCT uid FROM brewlog")
        uids = [row['uid'] for row in cursor.fetchall()]
        print(f"UIDs in database: {uids}")
        
        # Try different query approaches
        if uid:
            # Try with the provided UID
            cursor.execute("SELECT COUNT(*) as count FROM brewlog WHERE uid = ?", (uid,))
            direct_count = cursor.fetchone()['count']
            print(f"Records with UID '{uid}': {direct_count}")
            
            # Try with string conversion
            str_uid = str(uid)
            cursor.execute("SELECT COUNT(*) as count FROM brewlog WHERE uid = ?", (str_uid,))
            str_count = cursor.fetchone()['count']
            print(f"Records with string UID '{str_uid}': {str_count}")
            
            # Try with LIKE
            cursor.execute("SELECT COUNT(*) as count FROM brewlog WHERE uid LIKE ?", (f"%{uid}%",))
            like_count = cursor.fetchone()['count']
            print(f"Records with LIKE '%{uid}%': {like_count}")
        
        # Show sample records
        query = """
            SELECT 
                brewlog.*,
                beans.bean_name
            FROM 
                brewlog
            JOIN 
                beans ON brewlog.bean_id = beans.bean_id
            LIMIT 5
        """
        cursor.execute(query)
        rows = cursor.fetchall()
        
        print("\n=== SAMPLE RECORDS ===")
        for row in rows:
            print(f"ID: {row['id']}, UID: '{row['uid']}' (type: {type(row['uid'])}), Bean: {row['bean_name']}")
        
        conn.close()
        
        print("\n=== TEST COMPLETE ===")
        
    except Exception as e:
        print(f"Error testing brew records: {str(e)}")
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    # If a UID is provided as a command-line argument, use it
    if len(sys.argv) > 1:
        test_uid = sys.argv[1]
        test_fetch_brew_records(test_uid)
    else:
        test_fetch_brew_records()
