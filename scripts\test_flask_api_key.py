#!/usr/bin/env python3
"""
Test what API key Flask is actually using
"""

import os
from dotenv import load_dotenv

def test_flask_api_key():
    print("🔍 Testing Flask API Key Loading")
    print("=" * 50)
    
    # Load environment like Flask does
    load_dotenv()
    
    # Check what Flask will get
    api_key = os.environ.get('COFFEE_API_KEY', 'demo-key-12345-67890-abcdef')
    
    print(f"🔑 Flask will use API key: {api_key}")
    
    if api_key == 'demo-key-12345-67890-abcdef':
        print("❌ Flask is using the DEMO key!")
        print("This means .env is not being loaded properly")
    else:
        print("✅ Flask is using the real API key from .env")
    
    # Check if .env file exists
    if os.path.exists('.env'):
        print("✅ .env file exists")
        with open('.env', 'r') as f:
            content = f.read()
            if 'COFFEE_API_KEY' in content:
                print("✅ COFFEE_API_KEY found in .env file")
            else:
                print("❌ COFFEE_API_KEY NOT found in .env file")
    else:
        print("❌ .env file does NOT exist")

if __name__ == "__main__":
    test_flask_api_key()
