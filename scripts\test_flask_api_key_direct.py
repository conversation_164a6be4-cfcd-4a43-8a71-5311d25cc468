#!/usr/bin/env python3
"""
Test what API key Flask is using directly
"""

import os
import sys
from dotenv import load_dotenv

def test_flask_api_key_direct():
    print("🔍 Testing Flask API Key Direct")
    print("=" * 50)
    
    # Load environment like Flask does
    load_dotenv()
    
    # Check what we get
    api_key = os.environ.get('COFFEE_API_KEY', 'demo-key-12345-67890-abcdef')
    print(f"🔑 API key from os.environ: {api_key}")
    
    if api_key == 'demo-key-12345-67890-abcdef':
        print("❌ Using DEMO key - .env not loaded properly!")
        
        # Check if .env file exists
        if os.path.exists('.env'):
            print("✅ .env file exists")
            with open('.env', 'r') as f:
                content = f.read()
                if 'COFFEE_API_KEY' in content:
                    print("✅ COFFEE_API_KEY found in .env file")
                    # Extract the key manually
                    for line in content.split('\n'):
                        if line.startswith('COFFEE_API_KEY='):
                            manual_key = line.split('=', 1)[1].strip('"\'')
                            print(f"🔑 Manual extraction: {manual_key}")
                else:
                    print("❌ COFFEE_API_KEY NOT found in .env file")
        else:
            print("❌ .env file does NOT exist")
    else:
        print("✅ Using real API key from .env")
    
    # Now test importing Flask app
    try:
        sys.path.append('.')
        sys.path.append('..')
        
        print("\n🧪 Testing Flask app import...")
        from app import app
        
        # Test the route function directly
        with app.app_context():
            # Simulate what the route does
            route_api_key = os.environ.get('COFFEE_API_KEY', 'demo-key-12345-67890-abcdef')
            print(f"🌐 Flask route would use: {route_api_key}")
            
            if route_api_key == 'demo-key-12345-67890-abcdef':
                print("❌ Flask route is using DEMO key!")
            else:
                print("✅ Flask route is using real API key")
                
    except Exception as e:
        print(f"❌ Error importing Flask app: {e}")

if __name__ == "__main__":
    test_flask_api_key_direct()
