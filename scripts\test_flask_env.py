#!/usr/bin/env python3
"""
Test Flask App Environment Loading
"""

import os
import sys

def test_flask_env():
    print("🧪 Testing Flask Environment Loading")
    print("=" * 50)
    
    try:
        # Import Flask app (this will load .env via load_dotenv())
        from app import app
        
        # Check if COFFEE_API_KEY is loaded
        api_key = os.environ.get('COFFEE_API_KEY')
        
        if api_key:
            print(f"✅ Flask loaded COFFEE_API_KEY: {api_key[:20]}...")
        else:
            print("❌ Flask did NOT load COFFEE_API_KEY from .env!")
            print("This is the problem!")
        
        # Test the validation function directly
        from app import validate_api_key
        
        if api_key:
            result = validate_api_key(api_key)
            if result:
                print("✅ validate_api_key() works correctly")
                print(f"Key info: {dict(result)}")
            else:
                print("❌ validate_api_key() returned None!")
        
    except Exception as e:
        print(f"❌ Error testing Flask: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_flask_env()
