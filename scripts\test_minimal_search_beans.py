#!/usr/bin/env python3
"""
Test the minimal search_beans template to see if API key is rendered
"""

import requests
import re

def test_minimal_search_beans():
    print("🔍 Testing Minimal Search Beans Template")
    print("=" * 50)
    
    try:
        response = requests.get("http://127.0.0.1:5000/debug_search_beans_minimal", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to fetch minimal search_beans: {response.status_code}")
            print(f"Response: {response.text[:500]}...")
            return
        
        html_content = response.text
        
        # Look for the API key meta tag
        meta_pattern = r'<meta name="api-key" content="([^"]+)">'
        match = re.search(meta_pattern, html_content)
        
        if match:
            api_key = match.group(1)
            print(f"✅ Found API key meta tag: {api_key[:20]}...")
            
            if api_key == 'demo-key-12345-67890-abcdef':
                print("❌ Using DEMO key in template!")
            else:
                print("✅ Using real API key in template!")
        else:
            print("❌ API key meta tag NOT found!")
            
            # Look for any meta tags
            meta_tags = re.findall(r'<meta[^>]+>', html_content)
            print(f"Found {len(meta_tags)} meta tags:")
            for i, tag in enumerate(meta_tags[:5]):
                print(f"  {i+1}: {tag}")
                
        # Check for demo key in JavaScript
        if 'demo-key-12345-67890-abcdef' in html_content:
            print("⚠️  Found demo key fallback in JavaScript")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_minimal_search_beans()
