/* Responsive CSS for Israeli Coffee website */

/* Base responsive styles */

/* Touch-friendly styles */
button,
a.btn,
input[type="submit"],
input[type="button"],
.custom-btn,
.accessibility-button,
.modal-btn {
  min-height: 36px; /* Reduced from 44px for header buttons */
  min-width: 36px; /* Reduced from 44px for header buttons */
  touch-action: manipulation;
}

/* Keep larger touch targets for non-header elements */
.custom-btn,
.modal-btn,
#accessibility-widget {
  min-height: 30px;
  min-width: 44px;
}

/* Reduced size for accessibility modal elements */
.accessibility-button,
.accessibility-close {
  min-height: 31px;
  min-width: 31px;
}

/* Toggle buttons in search_beans need to be smaller with adequate text padding */
.toggle-button {
  min-height: 32px !important;
  min-width: 32px !important;
  padding-left: 10px !important;
  padding-right: 10px !important;
  box-sizing: border-box !important;
}

/* Specific styling for roasters, cities, origin and flavors toggle buttons */
#open-roaster-modal,
#open-cities-modal,
#open-origin-modal,
#open-flavors-modal {
  margin-right: 5px !important;
  margin-left: 5px !important;
  margin-top: 5px !important;
  margin-bottom: 5px !important;
}

/* Make modern-select height compatible with toggle buttons */
.modern-select {
  height: 32px !important;
  min-height: 32px !important;
  box-sizing: border-box !important;
  padding-right: 30px !important; /* Added padding-right for dropdown arrow space */
}

/* Specific styles for processing, roast_level, and elevation selects */
#processing, #roast_level, #elevation {
  padding-right: 30px !important; /* Extra padding to ensure enough space for dropdown arrow */
}

/* Remove hover effects on touch devices */
@media (hover: none) {
  a:hover,
  button:hover,
  .custom-btn:hover,
  .accessibility-button:hover,
  .accessibility-close:hover,
  .close-modal:hover {
    transform: none !important;
    background-color: inherit !important;
  }
}

/* Header height reduction for all screen sizes */
header {
  max-height: 50px !important;
  overflow: hidden;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
}

/* Header button styles */
header button {
  font-size: 14px !important;
  padding: 4px 8px !important;
  min-height: 36px !important;
  background-color: #e5e7eb !important; /* Light gray (Tailwind gray-200) */
  border: none !important;
  color: #333 !important;
}

/* Override any default button styles */
#homeBtn, #loginBtn, #logoutBtn, #brewLogBtn {
  background-color: #e5e7eb !important; /* Light gray (Tailwind gray-200) */
  color: #333 !important;
  border: none !important;
}

/* Add spacing between buttons */
#loginBtn, #logoutBtn, #brewLogBtn {
  margin-right: 1rem !important; /* 16px of space */
}

/* Ensure spacing on mobile */
@media (max-width: 640px) {
  #homeBtn {
    margin-right: 0.5rem !important; /* Reduced space on mobile */
    margin-bottom: 0 !important; /* No vertical margin needed */
  }

  /* Make buttons smaller on mobile */
  header button {
    padding: 2px 6px !important;
    min-width: 70px !important;
    font-size: 13px !important;
  }
}

/* Hover state for non-touch devices */
@media (hover: hover) {
  header button:hover,
  #homeBtn:hover,
  #loginBtn:hover,
  #logoutBtn:hover,
  #brewLogBtn:hover {
    background-color: #d1d5db !important; /* Slightly darker gray (Tailwind gray-300) */
    transition: background-color 0.2s ease !important;
  }
}

#user-name {
  font-size: 14px !important;
}

/* Small devices (phones, 640px and down) */
@media (max-width: 640px) {
  h1 {
    font-size: 3rem !important; /* Increased by 30% from 1.5rem */
    line-height: 1.4 !important; /* Increased line height for better readability */
    margin-bottom: 0.75rem !important; /* More space between headings */
  }

  /* Main title - Israeli.Coffee */
  .font-rammetto {
    font-size: 2.25rem !important; /* Increased by 30% from 1.75rem */
  }

  h2 {
    font-size: 1.5rem !important;
    line-height: 1.4 !important;
  }

  /* Specific styles for the subtitle headings */
  .font-varela {
    line-height: 1.6 !important; /* Even more spacing for the Hebrew text */
    margin-bottom: 1rem !important;
    font-size: 1.8rem !important; /* 18px - Increased by 30% from 14px */
  }

  /* Extra small devices */
  @media (max-width: 375px) {
    .font-varela {
      font-size: 1rem !important; /* 16px - Increased by 30% from 12px */
    }
  }

  .content {
    padding: 10px !important;
  }

  header {
    flex-direction: row !important; /* Keep header items in a row */
    padding: 2px 8px !important; /* Add some horizontal padding */
    max-height: none !important; /* Allow height to adjust on mobile */
    justify-content: flex-end !important; /* Align items to the right */
    align-items: center !important;
  }

  #auth-header {
    width: auto !important; /* Don't take full width */
    display: flex !important;
    flex-direction: row !important;
    justify-content: flex-end !important;
    align-items: center !important;
    margin-top: 0 !important;
    flex-wrap: nowrap !important; /* Prevent wrapping */
  }

  .custom-btn {
    width: 104px !important; /* Reduced by 20% from 130px */
    height: 104px !important; /* Reduced by 20% from 130px */
    font-size: 110% !important; /* Slightly smaller font */
    padding: 4px !important; /* Reduced padding */
  }
}

/* Medium devices (tablets, 768px and down) */
@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem !important;
  }

  .custom-btn {
    width: 120px !important; /* Reduced by 20% from 150px */
    height: 120px !important; /* Reduced by 20% from 150px */
    font-size: 130% !important; /* Slightly smaller font */
    padding: 6px !important; /* Reduced padding */
  }
}

/* Larger title sizes for non-mobile screens (min-width: 768px) */
@media (min-width: 768px) {
  /* Main title - Israeli.Coffee */
  .font-rammetto.md\:text-7xl {
    font-size: 7rem !important; /* Increased by 20% from 6xl */
  }

  .font-rammetto.lg\:text-8xl {
    font-size: 8rem !important; /* Increased by 20% from 7xl */
  }

  /* Subtitle headings */
  .font-varela.md\:text-4xl {
    font-size: 2.25rem !important; /* Increased by 20% from 3xl */
  }

  .font-varela.lg\:text-5xl {
    font-size: 3rem !important; /* Increased by 20% from 4xl */
  }
}

/* Form elements - make them more touch-friendly */
input, 
select, 
textarea {
font-size: 14px !important; /* Prevents iOS zoom on focus */
padding-right: 10px !important; /* Horizontal padding only */
min-height: 10px;
}


/* Increase spacing between interactive elements */
.form-group {
  margin-bottom: 15px !important;
}

/* Ensure modals are properly sized on mobile */
.modal-content {
  width: 90% !important;
  max-width: 400px !important;
  margin: 0 auto !important;
}

/* Ensure proper spacing in flex layouts on mobile */
.flex-container {
  gap: 10px !important;
}