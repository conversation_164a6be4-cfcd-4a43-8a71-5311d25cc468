document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contact-form');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(contactForm);
            
            fetch('/send_contact_email', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    alert(data.message);
                    // Clear the form
                    contactForm.reset();
                    
                    // Optionally redirect after a delay
                    if (data.redirect_url) {
                        setTimeout(() => {
                            window.location.href = data.redirect_url;
                        }, 2000);
                    }
                } else {
                    // Show error message
                    alert(data.message || 'אירעה שגיאה בעת שליחת ההודעה.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('אירעה שגיאה בעת שליחת ההודעה. אנא נסה שוב מאוחר יותר.');
            });
        });
    }
});
