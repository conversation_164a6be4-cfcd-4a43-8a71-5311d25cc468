{% extends "base.html" %}
{% block title %}ניהול בית קלייה{% endblock %}
{% block content %}
 <head>
      <meta charset="UTF-8">
      <title>הוספת פולי קפה</title>
      <style>
           body {font-family: sans-serif; margin: 20px; direction: rtl; text-align: right;}
            form label { display: block; margin-bottom: 5px; }
            form input, form select{ 
                margin-bottom: 10px; 
                padding: 5px; 
                direction: rtl;
                border: 2px solid #ccc;
                border-radius: 4px;
                box-sizing: border-box;
            }
            form input:focus, form select:focus {
                border-color: #4CAF50;
                outline: none;
            }
            .origin-container {
                margin-bottom: 15px;
            }
            .selected-countries {
                margin-bottom: 5px;
            }
            .country-tag {
                display: inline-block;
                background: #f0f0f0;
                padding: 3px 8px;
                margin: 2px;
                border-radius: 3px;
            }
            .remove-country {
                color: red;
                margin-right: 5px;
                cursor: pointer;
            }
            .modal {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
            }
            .modal-content {
                background: white;
                width: 300px;
                padding: 20px;
                margin: 100px auto;
                border-radius: 5px;
            }
            /* Add styles for flavors container */
            .flavors-container {
                margin-bottom: 15px;
            }
            .selected-flavors {
                margin-bottom: 5px;
            }
            .flavor-tag {
                display: inline-block;
                background: #f0f0f0;
                padding: 3px 8px;
                margin: 2px;
                border-radius: 3px;
            }
            .remove-flavor {
                color: red;
                margin-right: 5px;
                cursor: pointer;
            }
            /* Add styles for roast level container */
            .roast-level-container {
                margin-bottom: 15px;
            }
            .selected-roast-levels {
                margin-bottom: 5px;
            }
            .roast-level-tag {
                display: inline-block;
                background: #f0f0f0;
                padding: 3px 8px;
                margin: 2px;
                border-radius: 3px;
            }
            .remove-roast-level {
                color: red;
                margin-right: 5px;
                cursor: pointer;
            }
            /* Two-column layout styles */
            .two-col {
                display: flex;
                gap: 20px;
            }
            .column {
                flex: 1;
                min-width: 0;
            }
      </style>
 </head>
 <body>
      <h1>הוספת פולי קפה</h1>
      <form method="post">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <div class="two-col">
                <div class="column">
                    <!-- Left Column Fields -->
                    
                    <input type="hidden" name="roaster_id" value="{{ roaster_id }}">
                    <label>בית קלייה:</label>
                    <span>{{ roaster_name }}</span><br>
                   

                    <label for="bean_name">שם :</label>
                    <input type="text" name="bean_name" style="direction: rtl;"><br>

                    <label for="origin">מקור:</label>
                    <div class="origin-container">
                        <input type="hidden" name="origin" id="origin-input">
                        <div id="selected-countries" class="selected-countries"></div>
                        <div class="country-controls">
                            <select id="country-selector" style="direction: rtl;">
                                <option value="">בחר מדינה</option>
                                {% for country in countries %}
                                <option value="{{ country.country }}">{{ country.country }}</option>
                                {% endfor %}
                            </select>
                            <button type="button" id="add-new-country">+</button>
                        </div>
                    </div>

                    <label for="processing">עיבוד:</label>
                    <select name="processing" style="direction: rtl;">
                        <option value="אין מידע"> </option>
                        <option value="שטוף">שטוף</option>
                        <option value="נטורל (יבש)">נטורל (יבש)</option>
                        <option value="הוני פרוסס">הוני פרוסס</option>
                        <option value="מיוחד">מיוחד</option>
                    </select><br>

                    <label for="elevation">גובה:</label>
                    <input type="text" name="elevation" style="direction: rtl;"><br>

                    <label for="flavors">טעמים:</label>
                    <div class="flavors-container">
                        <input type="hidden" name="flavors" id="flavors-input">
                        <div id="selected-flavors" class="selected-flavors"></div>
                        <div class="flavor-controls">
                            <select id="flavor-selector" style="direction: rtl;">
                                <option value="">בחר טעם</option>
                                {% for flavor in flavors %}
                                <option value="{{ flavor.c_flavor }}">{{ flavor.c_flavor }}</option>
                                {% endfor %}
                            </select>
                            <button type="button" id="add-new-flavor">+</button>
                        </div>
                    </div>

                    <label for="acidity">חמיצות:</label>
                    <input type="text" name="acidity" style="direction: rtl;"><br>

                    <label for="after_taste">אחרי טעם:</label>
                    <input type="text" name="after_taste" style="direction: rtl;"><br>

                    <label for="roast_level">דרגת קלייה:</label>
                    <div class="roast-level-container">
                        <input type="hidden" name="roast_level" id="roast-level-input">
                        <div id="selected-roast-levels" class="selected-roast-levels"></div>
                        <select id="roast-level-selector" multiple size="5" style="direction: rtl;">
                            <option value="בהירה">בהירה</option>
                            <option value="בהירה-בינונית">בהירה-בינונית</option>
                            <option value="בינונית">בינונית</option>
                            <option value="בינונית-כהה">בינונית-כהה</option>
                            <option value="כהה">כהה</option>
                        </select>
                    </div><br>
                    <input type="submit" value="הוסף פולי קפה">
                </div>
                <div class="column">
                    <!-- Right Column Fields -->
                    <label for="body">גוף:</label>
                    <select name="body" style="direction: rtl;">
                        <option value="קל">קל</option>
                        <option value="בינוני">בינוני</option>
                        <option value="מלא">מלא</option>
                    </select><br>

                    <label for="arabica">ערביקה(%):</label>
                    <input type="number" name="arabica" style="direction: rtl;" value="{{ arabica|default('') }}"><br>

                    <label for="robusta">רובוסטה(%):</label>
                    <input type="number" name="robusta" style="direction: rtl;"><br>

                    <label for="mix">תערובת:</label>
                    <select id="mix" name="mix">
                        <option value="False" {% if mix|default('True') == 'False' %}selected{% endif %}>לא</option>
                        <option value="True" {% if mix|default('True') == 'True' %}selected{% endif %}>כן</option>
                    </select><br>

                    <label for="SCA_score">SCA Score:</label>
                    <input type="number" name="SCA_score" step="0.01" style="direction: rtl;"><br>

                    <label for="Specialty">Specialty:</label>
                    <select name="Speciality" required style="direction: rtl;">
                        <option value=False>לא</option>
                        <option value=True>כן</option>
                    </select><br>

                    <label for="decaf">נטול קפאין:</label>
                    <select name="decaf" required style="direction: rtl;">
                        <option value=False selected>לא</option>
                        <option value=True>כן</option>
                    </select><br>

                    <br>
                    <table>
                        <thead>
                            <tr>
                                <th>שיטות הכנה</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <label for="turkish">טורקי:</label>
                                </td>
                                <td>
                                    <input type="checkbox" name="turkish" value="True" checked>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label for="espresso">אספרסו:</label>
                                </td>
                                <td>
                                    <input type="checkbox" name="espresso" value="True" checked>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label for="french_press">פרנץ׳ פרס:</label>
                                </td>
                                <td>
                                    <input type="checkbox" name="french_press" value="True" checked>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label for="pour_over">פור אובר:</label>
                                </td>
                                <td>
                                    <input type="checkbox" name="pour_over" value="True" checked>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label for="drip">פילטר:</label>
                                </td>
                                <td>
                                    <input type="checkbox" name="drip" value="True" checked>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label for="cold_brew">קולד ברו:</label>
                                </td>
                                <td>
                                    <input type="checkbox" name="cold_brew" value="True" checked>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <label for="price">מחיר:</label>
                    <input type="number" name="price" step="0.01" value="{{ price|default('') }}" style="direction: rtl;"><br>

                    <label for="weight">משקל(ג):</label>
                    <input type="number" name="weight" style="direction: rtl;" value="{{ weight|default('1000') }}"><br>

                    <label for="image_file">שם קובץ תמונה:</label>
                    <input type="text" name="image_file" style="direction: rtl;"><br>
                </div>
            </div>
      </form>
    {% if roaster_id %}
        <a href="{{ url_for('manage_roaster', roaster_id_param=roaster_id) }}">חזור למסך קודם</a>
    {% else %}
        <a href="{{ url_for('manage_roaster') }}">חזור למסך קודם</a>
    {% endif %}

    <div id="country-modal" class="modal">
      <div class="modal-content">
          <h3>הוסף מדינה חדשה</h3>
          <input type="text" id="new-country-input" style="direction: rtl;">
          <div class="modal-buttons">
              <button type="button" id="save-country">שמור</button>
              <button type="button" id="cancel-country">ביטול</button>
          </div>
      </div>
  </div>

  <!-- Add modal dialog for new flavor at the end of the form -->
  <div id="flavor-modal" class="modal">
      <div class="modal-content">
          <h3>הוסף טעם חדש</h3>
          <input type="text" id="new-flavor-input" style="direction: rtl;">
          <div class="modal-buttons">
              <button type="button" id="save-flavor">שמור</button>
              <button type="button" id="cancel-flavor">ביטול</button>
          </div>
      </div>
  </div>

  <script>
    let selectedCountries = [];
    let selectedFlavors = [];
    let selectedRoastLevels = [];
    
    document.getElementById('country-selector').addEventListener('change', function() {
        const country = this.value;
        if (country && !selectedCountries.includes(country)) {
            selectedCountries.push(country);
            updateDisplay('country');
        }
        this.value = '';
    });

    document.getElementById('add-new-country').addEventListener('click', function() {
    document.getElementById('country-modal').style.display = 'block';
});

document.getElementById('save-country').addEventListener('click', function() {
    const newCountry = document.getElementById('new-country-input').value.trim();
    if (newCountry && !selectedCountries.includes(newCountry)) {
        selectedCountries.push(newCountry);
        updateDisplay('country');
        addNewCountryToDB(newCountry);
    }
    document.getElementById('country-modal').style.display = 'none';
    document.getElementById('new-country-input').value = '';
});

document.getElementById('cancel-country').addEventListener('click', function() {
    document.getElementById('country-modal').style.display = 'none';
    document.getElementById('new-country-input').value = '';
});

document.getElementById('flavor-selector').addEventListener('change', function() {
    const flavor = this.value;
    if (flavor && !selectedFlavors.includes(flavor)) {
        selectedFlavors.push(flavor);
        updateDisplay('flavor');
    }
    this.value = '';
});

document.getElementById('add-new-flavor').addEventListener('click', function() {
    document.getElementById('flavor-modal').style.display = 'block';
});

document.getElementById('save-flavor').addEventListener('click', function() {
    const newFlavor = document.getElementById('new-flavor-input').value.trim();
    if (newFlavor && !selectedFlavors.includes(newFlavor)) {
        selectedFlavors.push(newFlavor);
        updateDisplay('flavor');
        addNewFlavorToDB(newFlavor);
    }
    document.getElementById('flavor-modal').style.display = 'none';
    document.getElementById('new-flavor-input').value = '';
});

document.getElementById('cancel-flavor').addEventListener('click', function() {
    document.getElementById('flavor-modal').style.display = 'none';
    document.getElementById('new-flavor-input').value = '';
});

document.getElementById('roast-level-selector').addEventListener('change', function() {
    const selectedOptions = Array.from(this.selectedOptions).map(option => option.value);
    selectedRoastLevels = selectedOptions;
    updateDisplay('roast-level');
});

function updateDisplay(type) {
    if (type === 'country') {
        const container = document.getElementById('selected-countries');
        container.innerHTML = '';
        selectedCountries.forEach((country, index) => {
            const div = document.createElement('span');
            div.className = 'country-tag';
            div.innerHTML = `${country} <span class="remove-country" data-index="${index}">×</span>`;
            container.appendChild(div);
        });
        document.getElementById('origin-input').value = selectedCountries.join(', ');
    } else if (type === 'flavor') {
        const container = document.getElementById('selected-flavors');
        container.innerHTML = '';
        selectedFlavors.forEach((flavor, index) => {
            const div = document.createElement('span');
            div.className = 'flavor-tag';
            div.innerHTML = `${flavor} <span class="remove-flavor" data-index="${index}">×</span>`;
            container.appendChild(div);
        });
        document.getElementById('flavors-input').value = selectedFlavors.join(', ');
    } else if (type === 'roast-level') {
        const container = document.getElementById('selected-roast-levels');
        container.innerHTML = '';
        selectedRoastLevels.forEach((roastLevel, index) => {
            const div = document.createElement('span');
            div.className = 'roast-level-tag';
            div.innerHTML = `${roastLevel} <span class="remove-roast-level" data-index="${index}">×</span>`;
            container.appendChild(div);
        });
        document.getElementById('roast-level-input').value = selectedRoastLevels.join(', ');
    }
}

document.getElementById('selected-countries').addEventListener('click', function(e) {
    if (e.target.classList.contains('remove-country')) {
        const index = parseInt(e.target.dataset.index);
        selectedCountries.splice(index, 1);
        updateDisplay('country');
    }
});

document.getElementById('selected-flavors').addEventListener('click', function(e) {
    if (e.target.classList.contains('remove-flavor')) {
        const index = parseInt(e.target.dataset.index);
        selectedFlavors.splice(index, 1);
        updateDisplay('flavor');
    }
});

document.getElementById('selected-roast-levels').addEventListener('click', function(e) {
    if (e.target.classList.contains('remove-roast-level')) {
        const index = parseInt(e.target.dataset.index);
        selectedRoastLevels.splice(index, 1);
        updateDisplay('roast-level');
    }
});

function addNewCountryToDB(country) {
    fetch('/add_country', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ country: country }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Country added successfully');
        } else {
            console.error('Error adding country');
        }
    });
}

function addNewFlavorToDB(flavor) {
    fetch('/add_flavor', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ flavor: flavor }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Flavor added successfully');
        } else {
            console.error('Error adding flavor');
        }
    });
}
</script>
 </body>
</html>
{% endblock %}