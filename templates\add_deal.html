{% extends 'base.html' %}

{% block title %}הוספת מבצע{% endblock %}

{% block content %}
<div class='container mx-auto p-4'>
    <h1 class='text-2xl font-bold mb-6 text-center'>הוספת מבצע חדש</h1>
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class='flash-messages'>
                {% for category, message in messages %}
                    <div class='flash-message {{ category }}'>{{ message }}</div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    <form method='POST' class='bg-white p-6 rounded-lg shadow-md'>
        <input type='hidden' name='csrf_token' value='{{ csrf_token() }}'>
        <div class='mb-4'>
            <label for='roaster_id' class='block text-gray-700 text-sm font-bold mb-2'>בית קליה:</label>
            <select id='roaster_id' name='roaster_id' class='shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline'>
                {% for roaster in roasters %}
                    <option value='{{ roaster.roaster_id }}'>{{ roaster.name }}</option>
                {% endfor %}
            </select>
        </div>
        <div class='mb-4'>
            <label for='deal_text' class='block text-gray-700 text-sm font-bold mb-2'>המבצע:</label>
            <textarea id='deal_text' name='deal_text' rows='4' class='shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline' required></textarea>
        </div>
        <div class='mb-6'>
            <label for='coupon_code' class='block text-gray-700 text-sm font-bold mb-2'>קופון:</label>
            <input type='text' id='coupon_code' name='coupon_code' class='shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline'>
        </div>
        <button type='submit' class='bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline'>הוסף מבצע</button>
    </form>
</div>
{% endblock %}