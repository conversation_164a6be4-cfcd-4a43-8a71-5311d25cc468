{% extends "base.html" %}

{% block title %}הוספת בית קלייה{% endblock %}

{% block content %}
  <style>
    /* Styles specific to add_roaster form, consider moving to a CSS file if they grow */
    body { /* This might be overridden by base.html styles or can be removed if base.html handles body styling */
      font-family: sans-serif;
      margin: 20px;
      direction: rtl;
      text-align: right;
    }
    form label { display: block; margin-bottom: 5px; }
    form input, form textarea, select { margin-bottom: 10px; padding: 5px; direction: rtl; width: 100%; max-width: 400px; box-sizing: border-box; border: 1px solid #ccc;}
    input[type="submit"] { width: auto; }
  </style>

  <h1>הוספת בית קלייה</h1>
  <form method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    
    <label for="name">שם:</label>
    <input type="text" id="name" name="name" required><br>

    <label for="address">כתובת:</label>
    <input type="text" id="address" name="address"><br>

    <label for="city">עיר:</label>
    <input type="text" id="city" name="city"><br>

    <label for="zip">מיקוד:</label>
    <input type="number" id="zip" name="zip"><br>

    <label for="email">אימייל:</label>
    <input type="email" id="email" name="email"><br>

    <label for="webpage">דף אינטרנט:</label>
    <input type="text" id="webpage" name="webpage"><br>

    <label for="minimun_shipping">מינימום משלוח (₪):</label>
    <input type="number" id="minimun_shipping" name="minimun_shipping" step="any"><br>

    <label for="shipping_cost">עלות משלוח (₪):</label>
    <input type="number" id="shipping_cost" name="shipping_cost" step="any"><br>
    <br>
    <input type="submit" value="הוסף בית קלייה" class="btn btn-primary"> {# Example: Added a generic button class #}
  </form>
  
{% endblock %}