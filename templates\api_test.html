<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test Interface</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .pagination {
            margin-top: 20px;
            text-align: center;
        }
        .pagination button {
            margin: 0 5px;
            padding: 5px 10px;
            font-size: 12px;
        }
        .pagination button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>Coffee Beans API Test Interface</h1>
    
    <div class="container">
        <h2>API Configuration</h2>
        <div class="form-group">
            <label for="apiKey">API Key:</label>
            <input type="text" id="apiKey" placeholder="Enter your API key" value="coffee_api_demo_key_2024">
        </div>
        <div class="form-group">
            <label for="apiUrl">API URL:</label>
            <input type="text" id="apiUrl" value="/api/search_beans" readonly>
        </div>
    </div>

    <div class="container">
        <h2>Search Parameters</h2>
        
        <div class="form-group">
            <label for="origin">Origin:</label>
            <input type="text" id="origin" placeholder="e.g., Ethiopia, Brazil">
        </div>
        
        <div class="form-group">
            <label for="processing">Processing Method:</label>
            <select id="processing">
                <option value="">Any</option>
                <option value="Washed">Washed</option>
                <option value="Natural">Natural</option>
                <option value="Honey">Honey</option>
                <option value="Anaerobic">Anaerobic</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="roastLevel">Roast Level:</label>
            <select id="roastLevel">
                <option value="">Any</option>
                <option value="Light">Light</option>
                <option value="Medium">Medium</option>
                <option value="Dark">Dark</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="flavors">Flavors (comma-separated):</label>
            <input type="text" id="flavors" placeholder="e.g., chocolate, fruity, nutty">
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="arabica"> Arabica
            </label>
            <label>
                <input type="checkbox" id="robusta"> Robusta
            </label>
            <label>
                <input type="checkbox" id="speciality"> Speciality
            </label>
            <label>
                <input type="checkbox" id="decaf"> Decaf
            </label>
            <label>
                <input type="checkbox" id="israContent"> Israeli Content
            </label>
        </div>
        
        <div class="form-group">
            <label for="perPage">Results per page:</label>
            <select id="perPage">
                <option value="10">10</option>
                <option value="20" selected>20</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
        </div>
        
        <button onclick="searchBeans()">Search Beans</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div class="container">
        <h2>Results</h2>
        <div id="results" class="results" style="display: none;"></div>
        <div id="pagination" class="pagination" style="display: none;"></div>
    </div>

    <script>
        let currentPage = 1;
        let currentParams = {};

        function buildSearchData() {
            const data = {
                page: currentPage,
                per_page: parseInt(document.getElementById('perPage').value)
            };

            // Add filters only if they have values
            const origin = document.getElementById('origin').value.trim();
            if (origin) data.origin = origin.split(',').map(o => o.trim());

            const processing = document.getElementById('processing').value;
            if (processing) data.processing = processing;

            const roastLevel = document.getElementById('roastLevel').value;
            if (roastLevel) data.roast_level = roastLevel;

            const flavors = document.getElementById('flavors').value.trim();
            if (flavors) data.flavors = flavors.split(',').map(f => f.trim());

            if (document.getElementById('arabica').checked) data.arabica = 1;
            if (document.getElementById('robusta').checked) data.robusta = 1;
            if (document.getElementById('speciality').checked) data.speciality = 1;
            if (document.getElementById('decaf').checked) data.decaf = 1;
            if (document.getElementById('israContent').checked) data.isra_content = 1;

            return data;
        }

        async function searchBeans(page = 1) {
            currentPage = page;
            const apiKey = document.getElementById('apiKey').value;
            const apiUrl = document.getElementById('apiUrl').value;
            
            if (!apiKey) {
                showResults('Please enter an API key', 'error');
                return;
            }

            const data = buildSearchData();
            currentParams = data;

            try {
                showResults('Searching...', '');
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(result.error || `HTTP error! status: ${response.status}`);
                }

                displayResults(result);
                
            } catch (error) {
                showResults(`Error: ${error.message}`, 'error');
            }
        }

        function displayResults(result) {
            const resultsDiv = document.getElementById('results');
            const paginationDiv = document.getElementById('pagination');
            
            if (!result.data || result.data.length === 0) {
                showResults('No beans found matching your criteria.', 'success');
                paginationDiv.style.display = 'none';
                return;
            }

            let output = `Found ${result.pagination.total} beans (Page ${result.pagination.page} of ${result.pagination.total_pages})\n\n`;
            
            result.data.forEach((bean, index) => {
                output += `${index + 1}. ${bean.bean_name} (${bean.roaster_name})\n`;
                output += `   Origin: ${bean.origin || 'Unknown'}\n`;
                output += `   Processing: ${bean.processing || 'Unknown'}\n`;
                output += `   Roast: ${bean.roast_level || 'Unknown'}\n`;
                output += `   Price: ₪${bean.price || 'N/A'} for ${bean.weight || 'N/A'}g\n`;
                output += `   Flavors: ${bean.flavor || 'Not specified'}\n`;
                output += `   SCA Score: ${bean.SCA_score || 'N/A'}\n`;
                output += `   Speciality: ${bean.speciality ? 'Yes' : 'No'}\n`;
                output += `   Decaf: ${bean.decaf ? 'Yes' : 'No'}\n`;
                output += `   Israeli Content: ${bean.isra_content ? 'Yes' : 'No'}\n`;
                output += `   Brew Methods: ${getBrewMethods(bean)}\n`;
                output += `   Website: ${bean.roaster_webpage || 'N/A'}\n\n`;
            });

            showResults(output, 'success');
            
            // Display pagination
            if (result.pagination.total_pages > 1) {
                displayPagination(result.pagination);
            } else {
                paginationDiv.style.display = 'none';
            }
        }

        function getBrewMethods(bean) {
            const methods = [];
            if (bean.turkish) methods.push('Turkish');
            if (bean.espresso) methods.push('Espresso');
            if (bean.french_press) methods.push('French Press');
            if (bean.pour_over) methods.push('Pour Over');
            if (bean.drip) methods.push('Drip');
            if (bean.cold_brew) methods.push('Cold Brew');
            return methods.length > 0 ? methods.join(', ') : 'Not specified';
        }

        function displayPagination(pagination) {
            const paginationDiv = document.getElementById('pagination');
            paginationDiv.style.display = 'block';
            paginationDiv.innerHTML = '';

            // Previous button
            const prevBtn = document.createElement('button');
            prevBtn.textContent = 'Previous';
            prevBtn.disabled = pagination.page <= 1;
            prevBtn.onclick = () => searchBeans(pagination.page - 1);
            paginationDiv.appendChild(prevBtn);

            // Page info
            const pageInfo = document.createElement('span');
            pageInfo.textContent = ` Page ${pagination.page} of ${pagination.total_pages} `;
            paginationDiv.appendChild(pageInfo);

            // Next button
            const nextBtn = document.createElement('button');
            nextBtn.textContent = 'Next';
            nextBtn.disabled = pagination.page >= pagination.total_pages;
            nextBtn.onclick = () => searchBeans(pagination.page + 1);
            paginationDiv.appendChild(nextBtn);
        }

        function showResults(message, type) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.style.display = 'block';
            resultsDiv.textContent = message;
            resultsDiv.className = 'results ' + type;
        }

        function clearResults() {
            document.getElementById('results').style.display = 'none';
            document.getElementById('pagination').style.display = 'none';
            currentPage = 1;
            currentParams = {};
        }

        // Handle Enter key in API key field
        document.getElementById('apiKey').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchBeans();
            }
        });
    </script>
</body>
</html>