{% extends 'base.html' %}

{% block content %}
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>מבצעי קפה</title>
  <style>
    body {
        font-family: 'Varela Round', sans-serif;
        font-size: 14px;
    }
    table {
        width: 100%;
        font-family: 'Varela Round', sans-serif;
        font-size: 14px;
        border-collapse: collapse;
    }
    th.title {
        background-color: darkblue;
        color: white;
        position: sticky;
        top: 0;
        z-index: 1;
    }
    tr:nth-child(even) {
        background-color: #ddeeff;
    }
    tr:nth-child(odd) {
        background-color: white;
    }
    th, td {
        line-height: 2em;
        height: 4em;
        overflow: hidden;
        text-overflow: ellipsis;
        border: 1px solid #ddd;
        padding: 4px 8px;
    }
    .centered {
        text-align: center;
    }
    .page-title {
        text-align: center;
        margin: 20px 0;
        font-size: 24px;
        color: #333;
    }
    .no-records {
        text-align: center;
        margin: 50px 0;
        color: #666;
    }
    /* Add styles for the delete button */
    .delete-btn {
      background-color: #ff3333;
      color: white;
      border: none;
      border-radius: 3px;
      padding: 3px 8px;
      cursor: pointer;
      font-size: 12px;
    }
    .delete-btn:hover {
      background-color: #cc0000;
    }
    
    /* Add a style for the actions column */
    .actions-col {
      width: 60px;
      text-align: center;
    }

    /* Add mobile-specific styles for fixed header and scrollable table */
    @media (max-width: 640px) {
      /* Container for the deals section */
      .brew-logs-container {
        display: flex;
        flex-direction: column;
        height: 100vh; /* Use full viewport height */
        padding-bottom: 10px;
      }
      
      /* Fix the header at the top */
      .brew-logs-header {
        flex: 0 0 auto; /* Don't grow or shrink */
        padding-bottom: 10px;
        position: sticky;
        top: 0;
        background-color: white; /* Ensure header has a background */
        z-index: 10;
      }
      
      /* Allow only the table container to scroll */
      .brew-logs-table-container {
        flex: 1; /* Take remaining space */
        overflow-y: auto; /* Enable vertical scrolling */
        -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
      }
      
      /* Ensure the table takes full width */
      .brew-logs-table {
        width: 100%;
      }
      
      /* Make the table header sticky */
      .brew-logs-table thead th {
        position: sticky;
        top: 0;
        z-index: 5;
        background-color: darkblue;
        color: white;
      }
      
      /* Apply high-contrast mode styles when needed */
      body.high-contrast .brew-logs-header {
        background-color: black;
      }
      
      body.high-contrast .brew-logs-table thead th {
        background-color: black;
        border-bottom: 1px solid white;
      }
      
      /* Apply light background mode styles when needed */
      body.light-background .brew-logs-header {
        background-color: white;
      }
      
      body.light-background .brew-logs-table thead th {
        background-color: #f8f8f8;
        border-bottom: 1px solid #ccc;
      }

      /* Hardcoded column widths for mobile view */
      .brew-logs-table col:nth-child(1) { width: 150px; } /* בית קלייה */
      .brew-logs-table col:nth-child(2) { width: 90px; }  /* תאריך */
      .brew-logs-table col:nth-child(3) { width: 250px; } /* טקסט הצעה */
      .brew-logs-table col:nth-child(4) { width: 100px; } /* קוד קופון */
      .brew-logs-table col:nth-child(5) { width: 60px; }  /* מחיקה */
    }
    .brew-logs-table a {
      color: #0000EE; /* Standard link blue */
      text-decoration: underline;
    }
  </style>
</head>
<body class="showbrew" dir="rtl">
    <div class="brew-logs-container">
        <div class="brew-logs-header">
            <h1 class="page-title">מבצעי קפה</h1>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="mb-4">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} p-3 rounded mb-2">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <div class="brew-logs-table-container">
            {% if deals and deals|length > 0 %}
                <table class="brew-logs-table">
                    <colgroup>
                        <col style="width: 150px;" /> <!-- בית קלייה -->
                        <col style="width: 90px;" /> <!-- תאריך -->
                        <col style="width: 250px;" /> <!-- טקסט הצעה -->
                        <col style="width: 100px;" /> <!-- קוד קופון -->
                        {% if is_admin %}
                            <col style="width: 60px;" /> <!-- מחיקה -->
                        {% endif %}
                    </colgroup>
                    <thead>
                        <tr>
                            <th class="title">בית קלייה</th>
                            <th class="title centered">תאריך</th>
                            <th class="title">המבצע</th>
                            <th class="title centered">קוד קופון</th>
                            {% if is_admin %}
                                <th class="title actions-col">מחיקה</th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for deal in deals %}
                            <tr>
                                <td><a href="{{ deal.roaster_webpage }}" target="_blank" rel="noopener noreferrer">{{ deal.roaster_name }}</a></td>
                                <td class="centered">{{ deal.deal_date.strftime('%d-%m-%Y') if deal.deal_date else '' }}</td>
                                <td>{{ deal.deal_text|safe }}</td>
                                <td class="centered">{{ deal.deal_code }}</td>
                                {% if is_admin %}
                                    <td class="centered actions-col">
                                        <form method="post" action="{{ url_for('delete_deal', deal_id=deal.deal_id) }}" style="display: inline-block;">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                            <button type="submit" class="delete-btn"
                                                    onclick="return confirm('האם אתה בטוח שברצונך למחוק הצעה זו?');">
                                                מחק
                                            </button>
                                        </form>
                                    </td>
                                {% endif %}
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="no-records">
                    <p>אין כרגע מבצעים.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        function confirmDelete(dealId) {
            if (confirm("האם אתה בטוח שברצונך למחוק הצעה זו?")) {
                fetch(`/deal/delete/${dealId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token() }}'
                    }
                })
                .then(response => {
                    if (response.ok) {
                        window.location.reload(); // Reload page to reflect changes
                    } else {
                        alert("שגיאה במחיקת ההצעה.");
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert("שגיאה במחיקת ההצעה.");
                });
            }
        }
    </script>
</body>
</html>
{% endblock %}