<!-- {% block title %}רשומות בית קלייה{% endblock %}
{% block content %} -->
<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
<style>
    body {font-family: sans-serif; margin: 20px; direction: rtl; text-align: right;}
    form label { display: block; margin-bottom: 5px; }
    form select { margin-bottom: 10px; padding: 5px; direction: rtl;}
    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
    table, th, td { border: 1px solid black; }
    th, td { padding: 10px; text-align: right; }

    /* Button styles consistent with search_beans.html */
    .action-button {
        padding: 4px 8px;
        border-radius: 4px;
        cursor: pointer;
        font-family: 'Varela Round', sans-serif;
        display: inline-block;
        text-align: center;
        min-width: 40px;
        margin: 1px;
        font-size: 12px;
    }

    /* Colors only when not in high-contrast mode */
    body:not(.high-contrast) .action-button {
        background-color: #4CAF50;
        color: white;
        border: none;
    }

    body:not(.high-contrast) .action-button:hover {
        background-color: #45a049;
    }

    /* Special style for delete button */
    body:not(.high-contrast) .delete-button {
        background-color: #e31111;
    }

    body:not(.high-contrast) .delete-button:hover {
        background-color: #c62828;
    }

    /* High contrast mode styles */
    body.high-contrast .action-button,
    body.high-contrast .delete-button {
        background-color: black !important;
        color: white !important;
        border: 1px solid white !important;
    }

    /* Light background mode */
    body.light-background .action-button {
        background-color: #4CAF50 !important;
        color: white !important;
    }

    body.light-background .delete-button {
        background-color: #e31111 !important;
        color: white !important;
    }
</style>
<div class="container" dir="rtl">
    <h2>רשומות בית קלייה: <span style="color: blue;">{{ roaster.name }}</span></h2>
    
      
    <div class="beans-management mt-4">
        <h3>רשימת פולי קפה</h3>
                
        {% if beans %}
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>שם פולי קפה</th>
                    <th>מקור</th>
                    <th>טעמים</th>
                    <th>דרגת קלייה</th>
                    <th>ערביקה</th>
                    <th>רובוסטה</th>
                    <th>תערובת</th>
                    <th>Specialty Coffee</th>
                    <th>נטול קפאין</th>
                    <th>מחיר</th>
                    <th>משקל</th>
                </tr>
            </thead>
            <tbody>
                {% for bean in beans %}
                <tr>
                    <td>{{ bean.bean_name }}</td>
                    <td>{{ bean.origin }}</td>
                    <td>{{ bean.flavors }}</td>
                    <td>{{ bean.roast_level }}</td>
                    <td>{{ bean.arabica }}</td>
                    <td>{{ bean.robusta }}</td>
                    <td>{% if bean.mix == 0 %}חד זני{% elif bean.mix == 1 %}תערובת{% endif %}</td>
                    <td>{% if bean.speciality == 1 %}כן{% endif %}</td>
                    <td>{% if bean.decaf == 1 %}כן{% endif %}</td>
                    <td>{{ bean.price }}</td>
                    <td>{{ bean.weight }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <p>לא נמצאו פולי קפה עבור בית הקלייה זה.</p>
        {% endif %}
    </div>
</div>


<!-- {% endblock %} -->
</head>
