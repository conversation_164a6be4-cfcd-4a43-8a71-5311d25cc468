{% extends "base.html" %}
{% block title %}עריכת בית קלייה{% endblock %}
{% block content %}
<style>
    body {font-family: sans-serif; margin: 20px; direction: rtl; text-align: right;}
    form label { display: block; margin-bottom: 5px; }
    form input, form textarea, select{ margin-bottom: 10px; padding: 5px; direction: rtl;}
    
    /* Enhanced button styles */
    button {
        background-color: #2e56f4; /* Blue background */
        color: white; /* White text */
        border: 2px solid #1a3dc9; /* Darker border for depth */
        border-radius: 6px; /* Rounded corners */
        padding: 8px 16px; /* Comfortable padding */
        font-weight: bold; /* Bold text */
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* Subtle shadow */
        transition: all 0.2s ease; /* Smooth transition */
        margin: 5px; /* Spacing between buttons */
        cursor: pointer; /* Hand cursor on hover */
        width: auto; /* Prevent full-width buttons */
        display: inline-block; /* Keep buttons to their content size */
        text-align: center; /* Center text */
    }
    
    button:hover {
        background-color: #1a3dc9; /* Darker blue on hover */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* Enhanced shadow */
        transform: translateY(-2px); /* Slight lift effect */
    }
    
    /* Save button (primary action) */
    button[type="submit"]:not(.btn-secondary) {
        background-color: #28a745; /* Green for save/confirm */
        border-color: #1e7e34; /* Darker green border */
    }
    
    button[type="submit"]:not(.btn-secondary):hover {
        background-color: #218838; /* Darker green on hover */
    }
    
    /* Cancel button (secondary action) */
    .btn-secondary {
        background-color: #6c757d; /* Gray for cancel/secondary */
        border-color: #545b62; /* Darker gray border */
    }
    
    .btn-secondary:hover {
        background-color: #545b62; /* Darker gray on hover */
    }
</style>
<div class="container" dir="rtl">
    <h1>עריכת בית קלייה: <span style="color: blue;">{{ roaster.name }}</span></h1>
    
    <form method="POST" action="{{ url_for('edit_roaster') }}">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <input type="hidden" name="name" value="{{ roaster.name }}">
        
        <label for="address">כתובת:</label>
        <input type="text" class="form-control" id="address" name="address" value="{{ roaster.address }}">
                     
        <label for="city">עיר:</label>
        <input type="text" class="form-control" id="city" name="city" value="{{ roaster.city }}">
              
        <label for="zip">מיקוד:</label>
        <input type="text" class="form-control" id="zip" name="zip" value="{{ roaster.zip }}">
                     
        <label for="email">דוא"ל:</label>
        <input type="email" class="form-control" id="email" name="email" value="{{ roaster.email }}">
          
        <label for="webpage">אתר:</label>
        <input type="url" class="form-control" id="webpage" name="webpage" value="{{ roaster.webpage }}">
            
        <label for="minimun_shipping">משלוח חינם מעל:</label>
        <input type="text" class="form-control" id="minimun_shipping" name="minimun_shipping" value="{{ roaster.minimun_shipping }}">
  
        <label for="shipping_cost">דמי משלוח:</label>
        <input type="text" class="form-control" id="shipping_cost" name="shipping_cost" value="{{ roaster.shipping_cost }}">
        <br><br>
        <div class="button-container" style="display: flex; gap: 10px;">
            <button type="submit" style="width: auto; min-width: 120px; max-width: 150px;">שמור שינויים</button>
        </form>
        <form method="post" action="{{ url_for('set_roaster_id') }}" style="display:inline;">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <input type="hidden" name="roaster_id" value="{{ roaster_id }}">
            <button type="submit" class="btn-secondary" style="width: auto; min-width: 80px; max-width: 100px;">ביטול</button>
        </form>
        </div>
</div>
{% endblock %}
