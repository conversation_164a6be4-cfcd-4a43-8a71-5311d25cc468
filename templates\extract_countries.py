import sqlite3

def extract_and_store_countries():
    conn = sqlite3.connect('coffee_database.db')
    cursor = conn.cursor()

    # Fetch all unique origins from the beans table
    cursor.execute('SELECT DISTINCT origin FROM beans')
    origins = cursor.fetchall()

    # Create a set to store unique country names
    countries_set = set()

    # Extract country names from the origin field
    for origin in origins:
        if origin[0]:
            country_list = origin[0].split(',')
            for country in country_list:
                countries_set.add(country.strip())

    # Insert unique country names into the countries table
    for country in countries_set:
        cursor.execute('INSERT INTO countries (country) VALUES (?)', (country,))

    conn.commit()
    conn.close()

if __name__ == "__main__":
    extract_and_store_countries()