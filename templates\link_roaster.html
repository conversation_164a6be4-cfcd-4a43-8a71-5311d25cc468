{% extends "base.html" %}

{% block title %}Link Roaster to User{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>חיבור בית קלייה לכתובת מייל</h2>
    <p>הכנס את פרטי המייל ובחר בית קלייה</p>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <form method="POST" action="{{ url_for('link_roaster') }}">
        {{ form.hidden_tag() }} {# Include CSRF token if using Flask-WTF #}

        <div class="mb-3">
            <label for="email" class="form-label">ב<PERSON>ר כתובת מייל</label>
            <select class="form-select" id="email" name="email" required style="border: 1px solid black;">
                <option selected disabled value="">בחר</option>
                {% for email_value, email_label in form.email.choices %}
                <option value="{{ email_value }}">{{ email_label }}</option>
                {% endfor %}
            </select>
        </div>

        <div class="mb-3">
            <label for="roaster_id" class="form-label">בחר בית קלייה</label>
            <select class="form-select" id="roaster_id" name="roaster_id" required>
                <option selected disabled value="">בחר</option>
                {% for roaster in roasters %}
                <option value="{{ roaster.roaster_id }}">{{ roaster.name }}</option>
                {% endfor %}
            </select>
        </div>

        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="unlink_roaster" name="unlink_roaster">
            <label class="form-check-label" for="unlink_roaster">מחיקה</label>
        </div>

        <button type="submit" class="btn btn-primary w-auto" style="background-color: #0d6efd; border-color: #0d6efd; color: white;">קישור בית קלייה</button>
    </form>
</div>

<script>
document.querySelector('form').addEventListener('submit', function(event) {
    event.preventDefault(); // Prevent default form submission

    const form = event.target;
    const formData = new FormData(form);
    const jsonData = {};
    formData.forEach((value, key) => {
        jsonData[key] = value;
    });

    // Add the checkbox value to jsonData
    jsonData['unlink_roaster'] = document.getElementById('unlink_roaster').checked;

    fetch(form.action, {
        method: form.method,
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': jsonData['csrf_token'] // Include CSRF token from form data
        },
        body: JSON.stringify(jsonData)
    })
    .then(response => response.json())
    .then(data => {
        alert(data.message); // Show popup message
        if (data.success) {
            // Redirect after user clicks OK on the alert
            window.location.href = "{{ url_for('home') }}";
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while linking the roaster.'); // Show generic error popup
    });
});
</script>
{% endblock %}