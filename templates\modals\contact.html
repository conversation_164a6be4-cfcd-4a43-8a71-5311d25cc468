<div class="text-right">
  <h3 class="text-lg font-bold mb-4">צור קשר</h3>
  
  <p class="mb-4">
    אנחנו תמיד שמחים לשמוע מכם! אם יש לכם שאלות, הצעות או משוב, אנא מלאו את הטופס למטה:
  </p>
  
  <form class="mb-6" action="{{ url_for('send_contact_email') }}" method="POST">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
    <div class="mb-4">
      <label for="name" class="block text-gray-700 font-bold mb-2">שם:</label>
      <input type="text" id="name" name="name" class="w-full p-2 border border-gray-300 rounded" required>
    </div>
    
    <div class="mb-4">
      <label for="email" class="block text-gray-700 font-bold mb-2">אימייל:</label>
      <input type="email" id="email" name="email" class="w-full p-2 border border-gray-300 rounded" required>
    </div>
    
    <div class="mb-4">
      <label for="subject" class="block text-gray-700 font-bold mb-2">נושא:</label>
      <input type="text" id="subject" name="subject" class="w-full p-2 border border-gray-300 rounded" required>
    </div>
    
    <div class="mb-4">
      <label for="message" class="block text-gray-700 font-bold mb-2">הודעה:</label>
      <textarea id="message" name="message" rows="5" class="w-full p-2 border border-gray-300 rounded" required></textarea>
    </div>
    
    <div class="flex justify-end">
      <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
        שלח הודעה
      </button>
    </div>
  </form>
  
  <script>
    document.querySelector('form').addEventListener('submit', async function(event) {
      event.preventDefault();
      
      const formData = new FormData(this);
      try {
        const response = await fetch('/send_contact_email', {
          method: 'POST',
          body: formData
        });
        
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        
        const data = await response.json();
        
        // Display response message
        const messageDiv = document.createElement('div');
        messageDiv.className = 'mt-4 p-3 ' + (data.success ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700') + ' rounded';
        messageDiv.textContent = data.message;
        this.parentNode.appendChild(messageDiv);
        
        // Optional: Redirect or other actions based on data.redirect_url
        if (data.redirect_url) {
          setTimeout(() => {
            window.location.href = data.redirect_url;
          }, 2000); // Redirect after 2 seconds
        }
      } catch (error) {
        console.error('Error:', error);
        const messageDiv = document.createElement('div');
        messageDiv.className = 'mt-4 p-3 bg-red-100 text-red-700 rounded';
        messageDiv.textContent = 'Error sending message: ' + error.message;
        this.parentNode.appendChild(messageDiv);
      }
    });
  </script>
  
    
  {% if message %}
  <div class="mt-4 p-3 {% if success %}bg-green-100 text-green-700{% else %}bg-red-100 text-red-700{% endif %} rounded">
    {{ message }}
  </div>
  {% endif %}
</div>
