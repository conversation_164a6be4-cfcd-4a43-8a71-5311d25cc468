<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>סקר שביעות רצון</title>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.5.4/umd/popper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive.css') }}">
    <style>
        body {
            direction: rtl;
            text-align: right;
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            padding-top: 20px;
        }
        .container {
            max-width: 800px;
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
            color: #343a40;
            margin-bottom: 20px;
        }
        .form-group label {
            font-weight: bold;
            margin-bottom: 10px;
            display: block;
        }
        .form-control-range {
            width: 100%;
        }
        .range-labels {
            display: flex;
            justify-content: space-between;
            margin-top: 5px;
            font-size: 0.9em;
            color: #6c757d;
            padding: 0 5px; /* Add padding to ensure labels are at the ends */
        }
        .feature-selection {
            margin-top: 30px;
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
        }
        .form-check {
            margin-bottom: 10px;
        }
        .form-check-label {
            margin-right: 1.5rem; /* Adjust for RTL checkbox alignment */
            margin-left: 0;
        }
        .form-check-input {
            margin-right: -1.5rem; /* Adjust for RTL checkbox alignment */
            margin-left: 0;
        }
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
            width: 100%;
            padding: 10px;
            font-size: 1.1em;
            margin-top: 20px;
        }
        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #0056b3;
        }
        .error-message {
            color: red;
            font-size: 0.9em;
            margin-top: 5px;
        }
        .current-value-display {
            display: block;
            text-align: right; /* Align to the right for RTL */
            margin-top: 5px;
            font-weight: bold;
            color: #007bff;
            padding-right: 5px; /* Small padding to align with other text */
        }
        .range-labels .range-start-label {
            text-align: right;
        }
        .range-labels .range-end-label {
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center">סקר שביעות רצון</h1>
        <form action="/submit_poll" method="POST" onsubmit="return validateForm()">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <input type="hidden" name="poll_id" value="{{ unique_id }}">
            <div class="form-group">
                <label for="q1">עד כמה היית מרוצה מהשימוש במנוע החיפוש למציאת קפה לפי פרמטרים כמו סוג, מקור וארומה?</label>
                <span class="current-value-display">ערך נוכחי: <span id="q1-current-value">5</span></span>
                <input type="range" class="form-control-range" id="q1" name="q1" min="1" max="10" value="5" oninput="document.getElementById('q1-current-value').innerText=this.value">
                <div class="range-labels">
                    <span class="range-start-label">1</span>
                    <span class="range-end-label">10</span>
                </div>
            </div>

            <div class="form-group">
                <label for="q2">עד כמה מצאת את האתר נוח לשימוש וידידותי למשתמש?</label>
                <span class="current-value-display">ערך נוכחי: <span id="q2-current-value">5</span></span>
                <input type="range" class="form-control-range" id="q2" name="q2" min="1" max="10" value="5" oninput="document.getElementById('q2-current-value').innerText=this.value">
                <div class="range-labels">
                    <span class="range-start-label">1</span>
                    <span class="range-end-label">10</span>
                </div>
            </div>

            <div class="form-group">
                <label for="q3">עד כמה המידע באתר עזר לך לבחור קפה שמתאים לטעם האישי שלך?</label>
                <span class="current-value-display">ערך נוכחי: <span id="q3-current-value">5</span></span>
                <input type="range" class="form-control-range" id="q3" name="q3" min="1" max="10" value="5" oninput="document.getElementById('q3-current-value').innerText=this.value">
                <div class="range-labels">
                    <span class="range-start-label">1</span>
                    <span class="range-end-label">10</span>
                </div>
            </div>

            <div class="form-group">
                <label for="q4">עד כמה השמירה על מתכוני חליטה הייתה שימושית עבורך?</label>
                <span class="current-value-display">ערך נוכחי: <span id="q4-current-value">5</span></span>
                <input type="range" class="form-control-range" id="q4" name="q4" min="0" max="10" value="5" oninput="document.getElementById('q4-current-value').innerText=this.value">
                <div class="range-labels">
                    <span class="range-start-label">0 <span style="font-size: 0.8em;">(לא רלוונטי)</span></span>
                    <span class="range-end-label">10</span>
                </div>
            </div>

            <div class="form-group">
                <label for="q5">עד כמה תמליצו על האתר לחברים או חובבי קפה אחרים?</label>
                <span class="current-value-display">ערך נוכחי: <span id="q5-current-value">5</span></span>
                <input type="range" class="form-control-range" id="q5" name="q5" min="1" max="10" value="5" oninput="document.getElementById('q5-current-value').innerText=this.value">
                <div class="range-labels">
                    <span class="range-start-label">1</span>
                    <span class="range-end-label">10</span>
                </div>
            </div>

            

            <div class="feature-selection">
                <h2>אנא בחרו שני פיצ'רים חדשים שתרצו לראות באתר:</h2>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="features" value="בתי קפה מעניינים בעולם" id="feature1">
                    <label class="form-check-label" for="feature1">
                        בתי קפה מעניינים בעולם
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="features" value="החלפת פולי קפה" id="feature2">
                    <label class="form-check-label" for="feature2">
                        החלפת פולי קפה
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="features" value="מנוע חיפוש למכונות קפה" id="feature3">
                    <label class="form-check-label" for="feature3">
                        מנוע חיפוש למכונות קפה
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="features" value="מעבדות וטכנאים למכונות קפה" id="feature4">
                    <label class="form-check-label" for="feature4">
                        מעבדות וטכנאים למכונות קפה
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="features" value="מבצעים" id="feature5">
                    <label class="form-check-label" for="feature5">
                        מבצעים
                    </label>
                </div>
                <div id="feature-error" class="error-message"></div>
            </div>

            <button type="submit" class="btn btn-primary">שלח סקר</button>
        </form>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        function validateForm() {
            const checkboxes = document.querySelectorAll('input[name="features"]:checked');
            const featureError = document.getElementById('feature-error');

            if (checkboxes.length !== 2) {
                featureError.innerText = 'אנא בחרו בדיוק שתי פיצ\'רים.';
                return false;
            } else {
                featureError.innerText = '';
            }
            
            // Submit the form and show success message
            const form = document.querySelector('form');
            const formData = new FormData(form);
            
            fetch('/submit_poll', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': '{{ csrf_token() }}',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('הסקר נקלט בהצלחה. תודה');
                    window.location.href = '/';
                } else {
                    alert(data.error || 'אירעה שגיאה בשליחת הסקר. אנא נסו שנית.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('אירעה שגיאה בשליחת הסקר. אנא נסו שנית.');
            });
            
            // Prevent the default form submission
            return false;
        }

        // Limit checkbox selection to maximum 2
        document.addEventListener('DOMContentLoaded', function() {
            const checkboxes = document.querySelectorAll('input[name="features"]');
            const featureError = document.getElementById('feature-error');
            
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const checkedBoxes = document.querySelectorAll('input[name="features"]:checked');
                    
                    if (checkedBoxes.length > 2) {
                        this.checked = false;
                        featureError.innerText = 'ניתן לבחור רק שתי אופציות';
                        setTimeout(() => {
                            featureError.innerText = '';
                        }, 3000);
                    } else {
                        featureError.innerText = '';
                    }
                });
            });
            
            // Update range input current value display
            const rangeInputs = document.querySelectorAll('input[type="range"]');
            rangeInputs.forEach(input => {
                const currentValueSpanId = input.id + '-current-value';
                const currentValueSpan = document.getElementById(currentValueSpanId);
                if (currentValueSpan) {
                    currentValueSpan.innerText = input.value;
                }
            });
        });
    </script>
</body>
</html>