{% extends 'base.html' %}

{% block title %}Reset Password{% endblock %}

{% block content %}
<div class="container mx-auto mt-10 p-5 max-w-md">
    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
        <h1 class="text-2xl font-bold mb-6 text-center text-gray-800">Reset Your Password</h1>
        
        {# Display flashed messages if any #}
        {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
            <div class="mb-4">
              {% for category, message in messages %}
                <div class="p-3 rounded mb-2 {{ 'bg-blue-100 text-blue-700 border border-blue-300' if category == 'info' else 'bg-yellow-100 text-yellow-700 border border-yellow-300' if category == 'warning' else 'bg-red-100 text-red-700 border border-red-300' if category == 'danger' else 'bg-green-100 text-green-700 border border-green-300' }}" role="alert">
                  {{ message }}
                </div>
              {% endfor %}
            </div>
          {% endif %}
        {% endwith %}

        <p class="mb-6 text-gray-600 text-center">
            Enter your new password for <strong>{{ email }}</strong>
        </p>

        <form action="{{ url_for('update_password') }}" method="POST" id="resetPasswordForm">
            <input type="hidden" name="token" value="{{ token }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
            
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="new_password">
                    New Password
                </label>
                <input 
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline focus:border-blue-500" 
                    id="new_password" 
                    name="new_password" 
                    type="password" 
                    placeholder="Enter new password"
                    required
                    minlength="6"
                >
                <p class="text-xs text-gray-500 mt-1">Password must be at least 6 characters long</p>
            </div>
            
            <div class="mb-6">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="confirm_password">
                    Confirm New Password
                </label>
                <input 
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline focus:border-blue-500" 
                    id="confirm_password" 
                    name="confirm_password" 
                    type="password" 
                    placeholder="Confirm new password"
                    required
                    minlength="6"
                >
            </div>
            
            <div class="flex items-center justify-between">
                <button 
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full" 
                    type="submit"
                    id="submitBtn"
                >
                    Update Password
                </button>
            </div>
        </form>
        
        <div class="text-center mt-6">
            <a href="{{ url_for('home') }}" class="text-blue-500 hover:text-blue-700 text-sm">
                Back to Home
            </a>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('resetPasswordForm');
    const newPasswordInput = document.getElementById('new_password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const submitBtn = document.getElementById('submitBtn');
    
    function validatePasswords() {
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        
        // Reset styles
        newPasswordInput.classList.remove('border-red-500');
        confirmPasswordInput.classList.remove('border-red-500');
        
        let isValid = true;
        
        // Check password length
        if (newPassword.length > 0 && newPassword.length < 6) {
            newPasswordInput.classList.add('border-red-500');
            isValid = false;
        }
        
        // Check if passwords match
        if (confirmPassword.length > 0 && newPassword !== confirmPassword) {
            confirmPasswordInput.classList.add('border-red-500');
            isValid = false;
        }
        
        return isValid;
    }
    
    // Real-time validation
    newPasswordInput.addEventListener('input', validatePasswords);
    confirmPasswordInput.addEventListener('input', validatePasswords);
    
    // Form submission validation
    form.addEventListener('submit', function(e) {
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        
        if (newPassword.length < 6) {
            e.preventDefault();
            alert('Password must be at least 6 characters long');
            newPasswordInput.focus();
            return;
        }
        
        if (newPassword !== confirmPassword) {
            e.preventDefault();
            alert('Passwords do not match');
            confirmPasswordInput.focus();
            return;
        }
        
        // Disable submit button to prevent double submission
        submitBtn.disabled = true;
        submitBtn.textContent = 'Updating...';
    });
});
</script>

<style>
.border-red-500 {
    border-color: #ef4444;
}
</style>
{% endblock %}
