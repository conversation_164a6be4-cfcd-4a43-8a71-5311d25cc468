<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>מכתב קפה ישראלי - בחר קולה</title>
    <style>
        body { font-family: Arial, sans-serif; direction: rtl; }
        .container { display: flex; flex-direction: row; }
        .menu { width: 250px; margin-left: 40px; }
        .letter-box { flex: 1; border: 1px solid #ccc; padding: 24px; border-radius: 8px; background: #fafafa; }
        .roaster-info { margin-bottom: 16px; }
        .roaster-email { font-weight: bold; }
        .roaster-link { direction: ltr; text-align: left; margin-bottom: 12px; }
        select { width: 100%; padding: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="menu">
            <label for="roaster-select">בחר קולה:</label>
            <select id="roaster-select"></select>
        </div>
        <div class="letter-box" id="letter-box">
            <!-- Letter content will be dynamically inserted here -->
        </div>
    </div>
    <script>
        // Load roasters and letter content from backend
        let roasters = [];
        let letter_paragraphs = [];

        async function fetchData() {
            const [roastersResp, letterResp] = await Promise.all([
                fetch('/api/roasters_csv'),
                fetch('/api/israeli_coffee_letter')
            ]);
            roasters = await roastersResp.json();
            letter_paragraphs = await letterResp.json();
            populateMenu();
            if (roasters.length > 0) {
                showLetter(roasters[0].roaster_id);
            }
        }

        function populateMenu() {
            const select = document.getElementById('roaster-select');
            select.innerHTML = '';
            roasters.forEach(r => {
                const opt = document.createElement('option');
                opt.value = r.roaster_id;
                opt.textContent = r.roaster_name;
                select.appendChild(opt);
            });
            select.addEventListener('change', () => showLetter(select.value));
        }

        // Helper: convert <QQ> to <br>, <LINK> to anchor, and <...> or plain URLs to clickable links
        function linkify(text, roasterLink) {
            console.log("Linkify input:", text); // Log input text

            // Replace <QQ> followed by a newline (from join) with a single <br>
            text = text.replace(/<QQ>\n/g, '<br>'); // Handle <QQ> at paragraph end specifically
            console.log("After <QQ>\\n replace:", text);

            // Replace remaining <QQ> with a single newline (br)
            text = text.replace(/<QQ>/g, '<br>'); // Changed from <br><br> to <br>
            console.log("After remaining <QQ> replace:", text); // Log after replacement

            // Replace <LINK> with the actual roaster link as a clickable anchor
            if (roasterLink) {
                text = text.replace(/<LINK>/g, `<a href="${roasterLink}" target="_blank">${roasterLink}</a>`);
            }
            // Convert <...> to links
            text = text.replace(/<([^ >]+)>/g, function(match, url) {
                // Basic check if it looks like a URL
                if (url.startsWith('http:') || url.startsWith('https:')) {
                    return `<a href="${url}" target="_blank">${url}</a>`;
                }
                return match; // Keep original if not a URL like <some_tag>
            });
            // Convert plain URLs to links (not already inside <a>)
            text = text.replace(/(^|\s)(https?:\/\/[^\s<]+)/g, function(match, prefix, url) {
                 // Basic checks to avoid double-linking or linking inside attributes
                 const index = text.indexOf(match);
                 const precedingText = text.substring(Math.max(0, index - 10), index);
                 if (/href=["']$/.test(precedingText)) { return match; }
                 const textBefore = text.substring(0, index);
                 const lastOpenTag = textBefore.lastIndexOf('<a');
                 const lastCloseTag = textBefore.lastIndexOf('</a>');
                 if (lastOpenTag > lastCloseTag) { return match; }

                return `${prefix}<a href="${url}" target="_blank">${url}</a>`;
            });

            // Replace remaining literal newlines (\n) AFTER other replacements
            text = text.replace(/\n/g, '<br>');
            console.log("After \\n replace:", text);

            // console.log("Linkify output:", text); // Log final output
            return text;
        }

        function showLetter(roaster_id) {
            const r = roasters.find(x => x.roaster_id == roaster_id);
            if (!r) return;
            const box = document.getElementById('letter-box');
            let html = `<div class="roaster-info">
                <div class="roaster-email">✉️ ${r.roaster_email || ''}</div>
                <div>לכבוד</div>
                <div>${r.roaster_name}</div>
            </div>`;
            // Join all paragraphs for full letter, pass roaster link for <LINK> replacement
            let letterText = letter_paragraphs.join('\n'); // Keep joining with \n
            html += `<div>${linkify(letterText, r.roaster_link)}</div>`; // linkify now handles the \n correctly
            box.innerHTML = html;
        }

        fetchData();
    </script>
</body>
</html>
