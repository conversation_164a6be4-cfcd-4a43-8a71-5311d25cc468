{% extends "base.html" %}
{% block title %}חיפוש פולי קפה{% endblock %}
{% block head %}
    {{ super() }}
    <meta name="api-key" content="{{ api_key }}">
{% endblock %}
{% block content %}
  <style>
    /* Styles for search_beans content */
    table {
        width: 100%;
        font-family: 'Varela Round', sans-serif; /* Applied Varela Round font to table */
        font-size: 14px; /* Decreased font size by 1px */
        border-collapse: collapse;
    }

    /* Position and structure styles that should apply regardless of high-contrast mode */
    th.title {
        position: sticky; /* Make the header sticky */
        top: 0; /* Stick to the top */
        z-index: 1; /* Ensure it stays above the table rows */
    }

    /* Standardize to use body:not(.high-contrast) consistently */
    body:not(.high-contrast) th.title {
        background-color: darkblue;
        color: white;
    }

    body:not(.high-contrast) tr:nth-child(even) {
        background-color: #ddeeff;
    }

    body:not(.high-contrast) tr:nth-child(odd) {
        background-color: white;
    }

    /* Added styles for toggle buttons */
    .toggle-button {
        padding: 3px 8px;
        margin-right: 0px;
        cursor: pointer;
        border-radius: 2px;
    }

    body:not(.high-contrast) .toggle-button {
        border: none;
        background-color: #f0f0f0;
        color: #000;
        transition: background-color 0.3s, color 0.3s;
    }

    body:not(.high-contrast) .toggle-button:hover {
        background-color: lightblue;
        color: white;
    }

    body:not(.high-contrast) .active {
        background-color: green; /* Changed from lightblue to green */
        color: white;
    }
    th, td {
        line-height: 2em; /* Ensures double-line height for all rows */
        height: 4em; /* Fixed height for two lines */
        overflow: hidden; /* Hide overflow text */
        text-overflow: ellipsis; /* Add ellipsis for overflow text */
    }

    /* Add border to table cells only when not in high-contrast mode */
    body:not(.high-contrast) th,
    body:not(.high-contrast) td {
        border: 1px solid #ddd;
    }

    /* Control horizontal scrolling based on screen size */
    @media (min-width: 641px) {
      #bean-table-container {
        overflow-x: auto; /* Allow horizontal scrolling for sticky column */
      }
    }
    /* The existing @media (max-width: 640px) rule already handles overflow-x: auto for mobile */

    /* --- General Sticky First Column Styles --- */
    table th:first-child,
    table td:first-child {
      position: sticky;
      right: 0; /* Use right: 0 for RTL sticky positioning */
      z-index: 15; /* Ensure it's above normal cells */
    }

    /* Ensure the top-left cell (intersection of sticky header and sticky first column) has highest z-index */
    table th:first-child {
      z-index: 25; /* Higher than both normal sticky header (z-index: 1) and sticky column cells (z-index: 15) */
    }

    /* Add background colors for the sticky column to match the row styling */
    body:not(.high-contrast) tr:nth-child(even) td:first-child {
      background-color: #ddeeff; /* Match even row background */
    }

    body:not(.high-contrast) tr:nth-child(odd) td:first-child {
      background-color: white; /* Match odd row background */
    }

    /* Add shadow for visual separation */
    body:not(.high-contrast) td:first-child,
    body:not(.high-contrast) th:first-child {
      box-shadow: 2px 0 5px rgba(0,0,0,0.1);
    }

    /* High contrast mode considerations */
    body.high-contrast th:first-child,
    body.high-contrast td:first-child {
      background-color: black !important;
      border-right: 2px solid yellow !important;
    }

    /* Light background mode */
    body.light-background th:first-child {
      background-color: #f8f8f8 !important; /* Match header */
    }
    body.light-background tr:nth-child(odd) td:first-child {
      background-color: #eee !important; /* Match odd row */
    }
    body.light-background tr:nth-child(even) td:first-child {
      background-color: #fff !important; /* Match even row */
    }
    /* --- End General Sticky First Column Styles --- */

    /* Center the specified columns */
    table th:nth-child(4), table td:nth-child(6),
    table th:nth-child(7), table td:nth-child(7),
    table th:nth-child(8), table td:nth-child(8),
    table th:nth-child(9), table td:nth-child(9),
    table th:nth-child(10), table td:nth-child(10),
    table th:nth-child(11), table td:nth-child(11),
    table th:nth-child(12), table td:nth-child(12),
    table th:nth-child(13), table td:nth-child(13),
    table th:nth-child(14), table td:nth-child(14),
    table th:nth-child(15), table td:nth-child(15),
    table th:nth-child(16), table td:nth-child(16),
    table th:nth-child(17), table td:nth-child(17),
    table th:nth-child(18), table td:nth-child(18),
    table th:nth-child(19), table td:nth-child(19),
    table th:nth-child(20), table td:nth-child(20),
    table th:nth-child(21), table td:nth-child(21) {
        text-align: center;
    }
   /* Add similar styles for roaster-modal */
   .roaster-modal {
      position: fixed;
      z-index: 9999 !important; /* Ensure roaster modal always on top */
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
  }

  /* Background color only when not in high-contrast mode */
  body:not(.high-contrast) .roaster-modal {
      background-color: rgba(0,0,0,0.4);
  }

  .roaster-modal-content {
      margin: 15% auto;
      padding: 20px;
      width: 80%;
  }

  /* Background and border only when not in high-contrast mode */
  body:not(.high-contrast) .roaster-modal-content {
      background-color: #fefefe;
      border: 1px solid #888;
  }

  /* Make modals occupy full screen on mobile devices */
  @media (max-width: 640px) {
      .roaster-modal-content,
      .origin-modal-content,
      .flavors-modal .origin-modal-content {
          margin: 0;
          padding: 2px;
          width: 100%;
          height: 100%;
          border-radius: 0;
          box-sizing: border-box;
          overflow-y: auto;
      }
  }

  .roaster-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 10px;
  }

  /* Adjust grid columns for mobile devices */
  @media (max-width: 640px) {
      .roaster-grid {
          grid-template-columns: repeat(3, 1fr);
          gap: 8px;
      }
  }

  .modal-container {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10000 !important;
  }

  .roaster-modal, .origin-modal, .flavors-modal {
    position: fixed; /* Use fixed for full screen */
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    pointer-events: auto;
    z-index: 10000 !important;
  }

  /* Background color only when not in high-contrast mode */
  body:not(.high-contrast) .roaster-modal,
  body:not(.high-contrast) .origin-modal,
  body:not(.high-contrast) .flavors-modal {
    background-color: rgba(0,0,0,0.4);
  }

  /* Styles for the origin modal */
  .origin-modal {
      position: fixed;
      z-index: 2;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
  }

  .origin-modal-content {
      margin: 15% auto;
      padding: 20px;
      width: 80%;
  }

  /* Background and border only when not in high-contrast mode */
  body:not(.high-contrast) .origin-modal-content {
      background-color: #fefefe;
      border: 1px solid #888;
  }

  .close-btn {
      float: right;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
  }

  /* Improve close button for mobile */
  @media (max-width: 640px) {
      .close-btn {
          font-size: 36px;
          padding: 10px;
          position: absolute;
          top: 5px;
          right: 5px;
          z-index: 1001;
      }
  }

  /* Color only when not in high-contrast mode */
  body:not(.high-contrast) .close-btn {
      color: #aaa;
  }

  body:not(.high-contrast) .close-btn:hover,
  body:not(.high-contrast) .close-btn:focus {
      color: black;
      text-decoration: none;
  }

  .origin-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 10px;
  }

  .origin-option {
      padding: 10px;
      cursor: pointer;
      text-align: center;
  }

  .originb-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 10px;
  }

  /* Adjust grid columns for mobile devices */
  @media (max-width: 640px) {
      .origin-grid {
          grid-template-columns: repeat(3, 1fr);
          gap: 8px;
      }

      .originb-grid {
          grid-template-columns: repeat(3, 1fr);
          gap: 8px;
      }

      .origin-option, .flavor-option, .city-option, .roaster-option {
          padding: 12px 5px;
          font-size: 14px;
      }

      /* Add title for modals on mobile */
      .roaster-modal-content::before,
      .origin-modal-content::before,
      .flavors-modal-content::before,
      .-modal-content::before {
          content: attr(data-title);
          display: block;
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 20px;
          margin-top: 5px;
          text-align: center;
      }

      /* Add more space at the top for the close button */
      .roaster-modal-content,
      .origin-modal-content,
      .flavors-modal .origin-modal-content {
          padding-top: 50px !important;
      }
  }

  .originb-option {
      padding: 10px;
      cursor: pointer;
      text-align: center;
  }
  /* Border only when not in high-contrast mode */
  body:not(.high-contrast) .origin-option {
      border: 1px solid #ddd;
  }
    .modern-select {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        padding: 5px 20px 10px 15px;
        font-size: 14px;
        /* font-family: 'Varela Round', sans-serif; */
        border-radius: 4px;
        cursor: pointer;
        min-width: 150px;
        min-height: 10px;
        text-align: right;
        direction: rtl;
    }

    /* Styles that should only apply when not in high-contrast mode */
    body:not(.high-contrast) .modern-select {
        border: 1px solid #ddd;
        background-color: white;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14L2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 12px center;
        transition: all 0.2s ease;
    }

    body:not(.high-contrast) .modern-select:hover {
        border-color: #aaa;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    body:not(.high-contrast) .modern-select:focus {
        outline: none;
        border-color: #4a90e2;
        box-shadow: 0 0 0 2px rgba(74,144,226,0.2);
    }

    .modern-select option {
        text-align: right;
        direction: rtl;
    }

    .select-container {
        position: relative;
        display: inline-block;
        margin: 5px 0;
    }

    .select-label {
        display: block;
        margin-bottom: 1px;
        font-weight: 100;
        text-align: right;
    }

    /* Color only when not in high-contrast mode */
    body:not(.high-contrast) .select-label {
        color: #333;
    }
   .roaster-link {
    text-decoration: underline;
    cursor: pointer;
  }

  /* Color only when not in high-contrast mode */
  body:not(.high-contrast) .roaster-link {
    color: blue;
  }

  #roasterDetailModal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }

  /* Background color only when not in high-contrast mode */
  body:not(.high-contrast) #roasterDetailModal {
    background-color: rgba(0,0,0,0.5);
  }

  #roasterDetailModal .modal-content {
    margin: 15% auto;
    padding: 20px;
    width: 80%;
    max-width: 500px;
    position: relative;
    text-align: right;
    direction: rtl;
  }

  /* Background and border only when not in high-contrast mode */
  body:not(.high-contrast) #roasterDetailModal .modal-content {
    background-color: white;
    border: 1px solid #888;
  }

  .roaster-name {
    text-decoration: underline;
    cursor: pointer;
    display: inline-block;
    padding: 2px 4px;
  }

  /* Color only when not in high-contrast mode */
  body:not(.high-contrast) .roaster-name {
    color: blue;
  }
  
  .roaster-name:hover {
    text-decoration: underline;
    background-color: rgba(0, 0, 255, 0.05);
  }

  /* Brew Modal Styles */
  .brew-button {
    padding: 4px 8px;  /* Reduced padding */
    border-radius: 4px;
    cursor: pointer;
    font-family: 'Varela Round', sans-serif;
    display: inline-block;
    text-align: center;
    min-width: 40px;  /* Reduced min-width */
    margin: 1px;      /* Reduced margin */
    font-size: 12px;  /* Smaller font size */
  }

  /* Colors only when not in high-contrast mode */
  body:not(.high-contrast) .brew-button {
    background-color: #4CAF50;
    color: white;
    border: none;
  }

  body:not(.high-contrast) .brew-button:hover {
    background-color: #45a049;
  }

  #brewModal {
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    display: none;
  }

  #brewModal .modal-content {
    margin: 5% auto;
    padding:5px;
    width: 90%;
    max-width: 600px;
    position: relative;
    direction: rtl;
    border-radius: 8px;
  }

  /* Background and border only when not in high-contrast mode */
  body:not(.high-contrast) #brewModal .modal-content {
    background-color: white;
    border: 1px solid #888;
  }

  .form-group {
    margin-bottom: 15px;
  }

  .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }

  .form-group input,
  .form-group select {
    width: 100%;
    padding: 8px;
    border-radius: 4px;
    font-family: 'Varela Round', sans-serif;
  }

  /* Border color only when not in high-contrast mode */
  body:not(.high-contrast) .form-group input,
  body:not(.high-contrast) .form-group select {
    border: 1px solid #ddd;
  }

  .save-button {
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-family: 'Varela Round', sans-serif;
    float: left;
  }

  /* Colors only when not in high-contrast mode */
  body:not(.high-contrast) .save-button {
    background-color: #4CAF50;
    color: white;
    border: none;
  }

  body:not(.high-contrast) .save-button:hover {
    background-color: #45a049;
  }

  /* Increase width of textarea fields for aroma, acidity, sweetness, and bitterness by 20% */
  .form-group textarea {
      width: 100%;
  }

  /* Add rule to enforce Varela Round for form controls within the brew-modal */
  #brewModal input,
  #brewModal textarea,
  #brewModal select {
      font-family: 'Varela Round', sans-serif !important;
  }

  /* High-contrast mode specific styles to ensure visibility */
  body.high-contrast .modal-title,
  body.high-contrast #beanNameTitle {
    color: white !important;
    background-color: black !important;
  }

  body.high-contrast .roaster-name,
  body.high-contrast .roaster-link {
    color: yellow !important;
    text-decoration: underline !important;
  }

  body.high-contrast .brew-button,
  body.high-contrast .toggle-button,
  body.high-contrast .save-button,
  body.high-contrast .search-button,
  body.high-contrast .reset-button {
    background-color: black !important;
    color: white !important;
    border: 1px solid white !important;
  }

  body.high-contrast .active {
    background-color: #333 !important;
    border: 2px solid yellow !important;
  }

  /* Ensure modal content is visible */
  body.high-contrast .roaster-modal-content,
  body.high-contrast .origin-modal-content,
  body.high-contrast #roasterDetailModal .modal-content,
  body.high-contrast #brewModal .modal-content {
    background-color: black !important;
    border: 2px solid white !important;
  }

  /* Fix for options and selectable items */
  body.high-contrast .origin-option,
  body.high-contrast .flavor-option,
  body.high-contrast .city-option,
  body.high-contrast .roaster-option {
    border: 1px solid white !important;
    color: white !important;
  }

  /* Fix for form elements */
  body.high-contrast input,
  body.high-contrast select,
  body.high-contrast textarea,
  body.high-contrast .form-group input,
  body.high-contrast .form-group select,
  body.high-contrast .form-group label {
    background-color: black !important;
    color: white !important;
    border: 1px solid white !important;
  }

  /* Specific fix for select background in Firefox */
  body.high-contrast .modern-select {
    background-image: none !important;
  }

  /* Fix for the table */
  body.high-contrast th.title,
  body.high-contrast td {
    color: white !important;
    background-color: black !important;
    border: 1px solid white !important;
  }

  /* Fix for close buttons */
  body.high-contrast .close-btn {
    color: yellow !important;
  }

  /* Light background mode overrides */
  body.light-background {
      background-color: white !important;
      color: black !important;
  }
  body.light-background th.title {
      background-color: #f8f8f8 !important;
      color: black !important;
  }
  body.light-background tr:nth-child(even) {
      background-color: #fff !important;
  }
  body.light-background tr:nth-child(odd) {
      background-color: #eee !important;
  }
  body.light-background table th,
  body.light-background table td {
      color: black !important;
      border: 1px solid #ccc !important;
  }
  body.light-background .toggle-button {
      background-color: #e0e0e0 !important;
      color: black !important;
  }
  body.light-background .active {
      background-color: #ccc !important;
      color: black !important;
  }
  /* Higher specificity override for toggle buttons */
  body .toggle-button {
      padding: 3px 10px !important;
  }

  /* Mobile-specific styles to ensure header has same height as buttons */
  @media (max-width: 640px) {
    header {
      min-height: 41px !important; /* Match the height of homeBtn and loginBtn (36px + 5px) */
      display: flex;
      align-items: center;
    }

    #auth-header {
      margin-top: 0 !important; /* Remove any top margin */
    }

    /* Ensure buttons maintain consistent height */
    #homeBtn, #loginBtn, #logoutBtn {
      min-height: 36px !important;
    }

    /* Make buttons and modern select elements occupy one line each */
    .toggle-button {
      display: block !important;
      width: 100% !important;
      margin: 5px 0 !important;
      box-sizing: border-box !important;
    }

    .modern-select {
      display: block !important;
      width: 100% !important;
      margin: 5px 0 !important;
      box-sizing: border-box !important;
    }

    .select-container {
      display: block !important;
      width: 100% !important;
      margin: 5px 0 !important;
    }

    /* Ensure inline-flex containers stack vertically */
    div[style*="display:inline-flex"],
    div[style*="display: inline-flex"] {
      display: block !important;
      width: 100% !important;
      margin-bottom: 5px !important;
    }
  }

  /* Modified flavors modal positioning */
  .flavors-modal .origin-modal-content {
    margin: 5% auto !important; /* Reduce top margin to position closer to header */
    margin-top: 20px !important; /* Position immediately below header */
  }

  /* Add specific z-index to ensure flavors modal appears above other content */
  .flavors-modal {
    z-index: 10001 !important;
  }

  /* Enhanced mobile-specific styles */
  @media (max-width: 640px) {
    /* Existing mobile styles */
    header {
      min-height: 41px !important;
      display: flex;
      align-items: center;
    }

    #auth-header {
      margin-top: 0 !important;
    }

    /* Ensure buttons maintain consistent height */
    #homeBtn, #loginBtn, #logoutBtn {
      min-height: 36px !important;
    }

    /* Make search form area smaller to maximize bean table space */
    div[style*="flex: 0 0 25%"] {
      flex: 0 0 auto !important;
      max-height: 180px;
      overflow-y: auto;
    }

    /* Ensure the table header sticks properly on mobile screens */
    th.title {
      position: sticky;
      top: 0;
      z-index: 20; /* Higher z-index to ensure it's above everything */
    }

    /* Optimize the table container for mobile touch scrolling */
    div[style*="flex: 1; overflow-y: auto;"] {
      -webkit-overflow-scrolling: touch;
      flex: 1 !important;
    }

    /* Adjust table cell sizes for better mobile viewing */
    td, th {
      padding: 6px 3px;
      font-size: 12px;
    }

    /* Hide less important columns on mobile to save space */
    table th:nth-child(11),
    table td:nth-child(11),
    table th:nth-child(12),
    table td:nth-child(12) {
      display: none;
    }

    /* Make buttons and select elements more touch-friendly */
    .toggle-button {
      display: block !important;
      width: 100% !important;
      margin: 5px 0 !important;
      box-sizing: border-box !important;
      padding: 8px 10px !important; /* Larger touch target */
    }

    .modern-select {
      display: block !important;
      width: 100% !important;
      margin: 5px 0 !important;
      box-sizing: border-box !important;
      height: 36px; /* Larger touch target */
    }

    .select-container {
      display: block !important;
      width: 100% !important;
      margin: 5px 0 !important;
    }

    /* Stack inline-flex containers vertically */
    div[style*="display:inline-flex"],
    div[style*="display: inline-flex"] {
      display: block !important;
      width: 100% !important;
      margin-bottom: 5px !important;
    }

    /* Adjust brew button for better mobile tapping */
    .brew-button {
      min-height: 32px;
      min-width: 60px;
      padding: 8px !important;
    }

    /* Make first column (bean name) sticky on horizontal scroll */
    table th:first-child,
    table td:first-child {
      position: sticky;
      left: 0;
      z-index: 15; /* Less than header z-index (20) but more than regular cells */
      min-width: 100px; /* Ensure enough width for bean names */
      max-width: 120px; /* Limit width on small screens */
    }

    /* Ensure the top-left cell (intersection of sticky header and sticky first column) has highest z-index */
    table th:first-child {
      z-index: 25; /* Higher than both normal sticky header and sticky column */
    }

    /* Add background colors for the sticky column to match the row styling */
    body:not(.high-contrast) tr:nth-child(even) td:first-child {
      background-color: #ddeeff; /* Same as the even row background */
    }

    body:not(.high-contrast) tr:nth-child(odd) td:first-child {
      background-color: white; /* Same as the odd row background */
    }

    /* Add shadow for visual separation */
    body:not(.high-contrast) td:first-child,
    body:not(.high-contrast) th:first-child {
      box-shadow: 2px 0 5px rgba(0,0,0,0.1);
    }

    /* High contrast mode considerations */
    body.high-contrast th:first-child,
    body.high-contrast td:first-child {
      background-color: black !important;
      border-right: 2px solid yellow !important;
    }

    /* Light background mode */
    body.light-background th:first-child,
    body.light-background td:first-child {
      background-color: #f8f8f8 !important;
    }

    body.light-background tr:nth-child(odd) td:first-child {
      background-color: #eee !important;
    }

    /* Make sure horizontal scrolling is enabled for the table container */
    div[style*="flex: 1; overflow-y: auto;"] {
      overflow-x: auto !important;
      -webkit-overflow-scrolling: touch;
    }

    /* Add special styling for the bean name column to improve readability */
    table td:first-child {
      font-weight: bold;
      font-size: 13px;
      white-space: normal; /* Allow text wrapping for names */
      line-height: 1.3;
      padding-right: 8px;
    }

    /* Make brewModal occupy full screen on mobile */
    #brewModal {
      width: 100% !important;
      height: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      border-radius: 0 !important;
    }

    #brewModal .modal-content {
      width: 100% !important;
      height: 100% !important;
      max-width: none !important;
      margin: 0 !important;
      padding: 5px !important;
      border-radius: 0 !important;
      box-sizing: border-box !important;
      display: flex !important;
      flex-direction: column !important;
      overflow-y: auto !important;
    }

    /* Increase font size for brewModal on mobile devices to 14px */
    #brewModal,
    #brewModal *:not(.close-btn) {
      font-size: 14px !important; /* Increased from 12px to 14px for mobile */
    }

    /* Keep title elements slightly larger */
    #brewModal .modal-title,
    #brewModal #beanNameTitle {
      font-size: 16px !important; /* Increased from 14px to 16px for mobile */
      font-weight: bold;
    }

    #brewModal #brewForm {
      margin-top: 85px !important; /* Increased top margin to avoid overlap with fixed title */
      overflow-y: auto !important;
    }
  }

  /* Decrease font size in brew modal to 12px, but only on non-mobile devices */
  @media (min-width: 641px) {
    #brewModal,
    #brewModal *:not(.close-btn) {
      font-size: 12px !important;
    }

    #brewModal .modal-title,
    #brewModal #beanNameTitle {
      font-size: 14px !important;
      font-weight: bold;
    }
  }

  /* Keep the close button large on all devices */
  #brewModal .close-btn {
    font-size: 48px !important; /* Override any other font size rules */
  }
  </style>

  <!-- ... rest of the file remains unchanged ... -->

  <script>
  // Set global variable for user login status by reading from body data attribute
  window.userLoggedIn = document.body.getAttribute('data-user-logged-in') === 'true';

  // ===== SINGLE CONSOLIDATED SCRIPT FOR BREW MODAL FUNCTIONALITY =====
  function validateNumeric(input) {
    const value = input.value;
    if (isNaN(value) || value.trim() === '') {
        input.setCustomValidity('Please enter a valid number');
    } else {
        input.setCustomValidity('');
    }
  }

  function openBrewModal(beanId, beanName) {
    document.getElementById('bean_id').value = beanId;
    // Ensure beanName is properly sanitized
    const sanitizedBeanName = typeof beanName === 'string' ? beanName : '';
    document.getElementById('beanNameTitle').textContent = "קפה: " + sanitizedBeanName;
    document.getElementById('brewModal').style.display = 'block';
  }

  function closeBrewModal() {
    document.getElementById('brewModal').style.display = 'none';
    document.getElementById('brewForm').reset(); // Reset form when closing
  }

  // This will run EXACTLY ONCE when the DOM is loaded
  if (typeof brewFormInitialized === 'undefined') {
    document.addEventListener('DOMContentLoaded', function() {
      window.brewFormInitialized = true;

      // Replace the form with a clone to remove any existing handlers
      const oldForm = document.getElementById('brewForm');
      const newForm = oldForm.cloneNode(true);
      oldForm.parentNode.replaceChild(newForm, oldForm);

      // Add our ONE event handler for form submission
      newForm.addEventListener('submit', function(e) {
        console.log("Form submission triggered - SINGLE HANDLER");
        e.preventDefault();

        // Get form values
        const formData = {
          bean_id: document.getElementById('bean_id').value,
          bean_purchase_date: document.getElementById('bean_purchase_date').value || null,
          bean_roasting_date: document.getElementById('bean_roasting_date').value || null,
          brew_method: document.getElementById('brew_method').value || null,
          grind_settings: document.getElementById('grind_settings').value ? parseFloat(document.getElementById('grind_settings').value) : null,
          brew_temp: document.getElementById('brew_temp').value ? parseFloat(document.getElementById('brew_temp').value) : null,
          PreInfusionTimeSec: document.getElementById('PreInfusionTimeSec').value ? parseInt(document.getElementById('PreInfusionTimeSec').value) : null,
          brew_time: document.getElementById('brew_time').value ? parseInt(document.getElementById('brew_time').value) : null,
          coffee_dose: document.getElementById('coffee_dose').value ? parseFloat(document.getElementById('coffee_dose').value) : null,
          coffee_output: document.getElementById('coffee_output').value ? parseFloat(document.getElementById('coffee_output').value) : null,
          aroma: document.getElementById('aroma').value || null,
          acidity: document.getElementById('acidity').value || null,
          sweetness: document.getElementById('sweetness').value || null,
          bitterness: document.getElementById('bitterness').value || null,
          body: document.getElementById('body').value || null,
          aftertaste: document.getElementById('aftertaste').value || null,
          overall_rating: document.getElementById('overall_rating').value ? parseInt(document.getElementById('overall_rating').value) : null
        };

        if (!formData.bean_id) {
          alert('שגיאה: חסר מזהה פולי קפה');
          return;
        }

        // Get CSRF token from meta tag OR form
        let csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
          csrfToken = csrfToken.getAttribute('content');
        } else {
          // Fallback: get from form
          csrfToken = document.querySelector('input[name="csrf_token"]').value;
        }



        fetch('/save_brew', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
          },
          body: JSON.stringify(formData)
        })
        .then(response => {
          if (!response.ok) {
            // Try to parse as JSON first, but handle HTML error pages too
            return response.text().then(text => {
              try {
                // Try to parse as JSON
                const jsonData = JSON.parse(text);
                return Promise.reject(jsonData);
              } catch (e) {
                // If not valid JSON, it's probably an HTML error page
                return Promise.reject({ error: 'Server returned HTML error page. Check CSRF token.' });
              }
            });
          }
          return response.json();
        })
        .then(data => {
          if (data.success) {
            alert('חליטה נשמרה בהצלחה');
            closeBrewModal();
          } else {
            alert('שגיאה בשמירת החליטה: ' + (data.error || 'Unknown error'));
          }
        })
        .catch(error => {
          alert('שגיאה בשמירת החליטה: ' + (error.error || error.message || JSON.stringify(error)));
        });
      });
    });
  }

  function displayBeans(beans) {
    const beanList = document.getElementById('beanList');
    beanList.innerHTML = '';

    // Create colgroup element to preserve column styles
    const colGroupHTML = `
    <colgroup>
      <col style="width: 170px;" /> <!-- שם -->
      <col style="width: 120px;" /> <!-- קולה -->
      <col style="width: 160px;" /> <!-- ארץ מוצא -->
      <col style="width: 60px;" /> <!-- עיבוד -->
      <col style="width: 170px;" /> <!-- טעמים -->
      <col style="width: 40px;" />  <!-- דרגת קליה -->
      <col style="width: 50px;" /> <!-- גוף -->
      <col style="width: 50px;" /> <!-- חמיצות -->
      <col style="width: 50px;" />  <!-- ערביקה -->
      <col style="width: 50px;" />  <!-- רובוסטה -->
      <col style="width: 50px;" />  <!-- סוג -->
      <col style="width: 40px;" id="scaCol" /> <!-- New column for SCA Score -->
      <col style="width: 60px;" /> <!-- Speciality Coffee -->
      <col style="width: 50px;" />  <!-- מחיר משקל (decreased) -->
      <col style="width: 60px;" />  <!-- מחיר 100 גר (increased) -->
      <col style="width: 40px;" />  <!-- טורקי -->
      <col style="width: 50px;" />  <!-- אספרסו -->
      <col style="width: 40px;" />  <!-- פרנץ' פרס -->
      <col style="width: 40px;" />  <!-- פור אובר -->
      <col style="width: 40px;" />  <!-- דריפ -->
      <col style="width: 40px;" />  <!-- קולד ברו -->
      <col style="width: 30px;" />  <!-- Changed brew column from 40px to 30px -->
    </colgroup>`;

    // Get the table element
    const table = document.querySelector('table');

    // Remove existing colgroup if it exists
    const existingColgroup = table.querySelector('colgroup');
    if (existingColgroup) {
      existingColgroup.remove();
    }

    // Insert the new colgroup at the beginning of the table
    table.insertAdjacentHTML('afterbegin', colGroupHTML);

    beans.forEach(bean => {
      const row = document.createElement('tr');

      // Escape any special characters in the bean name to prevent JavaScript errors
      const escapedBeanName = bean.bean_name.replace(/'/g, "\\'").replace(/"/g, '\\"');

      // Create bean name display with optional isra_content text
      let beanNameDisplay = bean.bean_name;
      if (bean.isra_content === true || bean.isra_content === 1) {
        beanNameDisplay += '<br><small style="color: #d4a853; font-weight: bold;">הקפה הטוב ביותר 2025</small>';
      }

      row.innerHTML = `
        <td>${beanNameDisplay}</td>
        <td class="roaster-name" onclick="showRoasterDetails('${bean.roaster_id}')">
          ${bean.roaster_name}
        </td>
        <td>${bean.origin}</td>
        <td>${bean.processing}</td>
        <td>${bean.flavors}</td>
        <td>${bean.roast_level}</td>
        <td>${bean.body || ''}</td>
        <td>${bean.acidity || ''}</td>
        <td>${bean.arabica}</td>
        <td>${bean.robusta}</td>
        <td>${bean.mix ? 'תערובת' : 'חד-זני'}</td>
        <td>${bean.SCA_score || ''}</td>
        <td>${bean.speciality ? 'כן' : 'לא'}</td>
        <td>${bean.price} ₪</td>
        <td>${calculatePricePer100g(bean.price, bean.weight).toFixed(1)} ₪</td>
        <td>${bean.turkish ? '✓' : ''}</td>
        <td>${bean.espresso ? '✓' : ''}</td>
        <td>${bean.french_press ? '✓' : ''}</td>
        <td>${bean.pour_over ? '✓' : ''}</td>
        <td>${bean.drip ? '✓' : ''}</td>
        <td>${bean.cold_brew ? '✓' : ''}</td>
        <td></td> <!-- Empty cell for brew button -->
      `;

      // Add the brew button with a direct event listener instead of an onclick attribute
      const brewButtonCell = row.cells[row.cells.length - 1];

      // Check if user is logged in (use global variable)
      const userLoggedIn = window.userLoggedIn;

      if (userLoggedIn) {
        const brewButton = document.createElement('button');
        brewButton.className = 'brew-button';
        brewButton.textContent = 'חליטה';
        brewButton.addEventListener('click', function() {
          openBrewModal(bean.bean_id, bean.bean_name);
        });
        brewButtonCell.appendChild(brewButton);
      } else {
        // For unregistered users, show disabled text or login prompt
        const loginPrompt = document.createElement('span');
        loginPrompt.textContent = 'התחבר לשמירה';
        loginPrompt.style.color = '#666';
        loginPrompt.style.fontSize = '12px';
        loginPrompt.style.cursor = 'pointer';
        loginPrompt.addEventListener('click', function() {
          // Open login modal
          const loginModal = document.getElementById('loginModal');
          if (loginModal) {
            loginModal.style.display = 'flex';
          }
        });
        brewButtonCell.appendChild(loginPrompt);
      }
      beanList.appendChild(row);
    });

    // Helper function to calculate price per 100g
    function calculatePricePer100g(price, weight) {
      return (price / weight) * 100;
    }
  }

  // For initial page load, modify the brew button onclick handler
  document.addEventListener('DOMContentLoaded', function() {
    const brewButtons = document.querySelectorAll('.brew-button');
    brewButtons.forEach(button => {
      const originalOnclick = button.getAttribute('onclick');
      if (originalOnclick && originalOnclick.includes('openBrewModal')) {
        // Extract the bean ID
        const beanIdMatch = originalOnclick.match(/openBrewModal\('([^']+)',/);
        if (beanIdMatch && beanIdMatch[1]) {
          const beanId = beanIdMatch[1];

          // Get the bean name from the first cell in the same row
          const row = button.closest('tr');
          const beanName = row ? row.cells[0].textContent : '';

          // Replace the onclick attribute with a safer version
          button.removeAttribute('onclick');
          button.addEventListener('click', function() {
            openBrewModal(beanId, beanName);
          });
        }
      }
    });
  });
</script>

<!-- Flavor mode toggle script moved to a single location below -->
<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script>
  $(function() {
    // Define Hebrew localization
    $.datepicker.regional['he'] = {
      closeText: 'סגור',
      prevText: '&#x3C;הקודם',
      nextText: 'הבא&#x3E;',
      currentText: 'היום',
      monthNames: ['ינואר','פברואר','מרץ','אפריל','מאי','יוני',
      'יולי','אוגוסט','ספטמבר','אוקטובר','נובמבר','דצמבר'],
      monthNamesShort: ['ינו','פבר','מרץ','אפר','מאי','יוני',
      'יולי','אוג','ספט','אוק','נוב','דצמ'],
      dayNames: ['ראשון','שני','שלישי','רביעי','חמישי','שישי','שבת'],
      dayNamesShort: ['א\'','ב\'','ג\'','ד\'','ה\'','ו\'','שבת'],
      dayNamesMin: ['א\'','ב\'','ג\'','ד\'','ה\'','ו\'','שבת'],
      weekHeader: 'Wk',
      dateFormat: 'dd/mm/yy',
      firstDay: 0,
      isRTL: true,
      showMonthAfterYear: false,
      yearSuffix: ''
    };

    // Set Hebrew as default
    $.datepicker.setDefaults($.datepicker.regional['he']);

    // Initialize the datepickers
    $("#bean_purchase_date, #bean_roasting_date").datepicker({
      changeMonth: true,
      changeYear: true,
      yearRange: "2000:2050"
    });
  });
</script>
<!-- Single flavor mode toggle script -->
<script>
  // Define flavorMode in the window scope so it's accessible everywhere
  window.flavorMode = "OR";

  document.addEventListener("DOMContentLoaded", function(){
      const toggleBtn = document.getElementById("toggleFlavorMode");
      // Set initial button text
      toggleBtn.textContent = window.flavorMode + " :התאמת חיפוש";

      // Add single event listener
      toggleBtn.addEventListener("click", function(){
         // Toggle the mode
         window.flavorMode = window.flavorMode === "OR" ? "AND" : "OR";
         // Update button text with the new mode
         this.textContent = window.flavorMode + " :התאמת חיפוש";
         // Update results with the new mode
         updateResults();
      });
  });
</script>
<!-- Override button styles for Search Beans page -->
<style>
  /* Custom search button styles */
  .search-button {
      padding: 8px 10;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
  }

  /* Colors only when not in high-contrast mode */
  body:not(.high-contrast) .search-button {
      background-color: #4285f4 !important;
      color: white !important;
      border: none;
  }

  body:not(.high-contrast) .search-button:hover {
      opacity: 0.9;
  }

  /* Override any base styles conflicting for container (if needed) */
  .search-container button {
      margin: 5px;
  }

  /* Override global button width for toggle buttons */
  .toggle-button {
      display: inline-block !important;
      width: auto !important;
      padding: 4px 1px !important;
      margin-right: 5px !important;
      white-space: nowrap !important;
      min-width: 0 !important;
  }

  /* New style for the reset search button - only colors when not in high-contrast mode */
  body:not(.high-contrast) .reset-button {
      background-color: #e31111 !important; /* Light red background */
      color: white !important;
  }

  /* Make all modals consistent on mobile devices */
  @media (max-width: 640px) {
      /* Full screen styling for all modals */
      .roaster-modal-content,
      .origin-modal-content,
      #cities-modal .origin-modal-content,
      .flavors-modal .origin-modal-content {
          margin: 0 !important;
          padding: 15px !important;
          width: 100% !important;
          height: 100% !important;
          border-radius: 0 !important;
          box-sizing: border-box !important;
          overflow-y: auto !important;
      }

      /* Consistent title styling for all modals */
      .roaster-modal-content::before,
      .origin-modal-content::before,
      #cities-modal .origin-modal-content::before,
      .flavors-modal .origin-modal-content::before {
          content: attr(data-title);
          display: block;
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 20px;
          margin-top: 40px;
          text-align: center;
      }

      /* Consistent padding for close button */
      .roaster-modal-content,
      .origin-modal-content,
      #cities-modal .origin-modal-content,
      .flavors-modal .origin-modal-content {
          padding-top: 50px !important;
      }

      /* Consistent close button styling */
      .roaster-modal .close-btn,
      .origin-modal .close-btn,
      #cities-modal .close-btn,
      .flavors-modal .close-btn {
          font-size: 36px !important;
          padding: 10px !important;
          position: absolute !important;
          top: 5px !important;
          right: 5px !important;
          z-index: 1001 !important;
      }
  }
</style>

  <div style="display: flex; flex-direction: column; height: 100vh;" dir="rtl">
    {% if not user_logged_in %}
    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; margin: 10px; border-radius: 5px; text-align: center; font-family: 'Varela Round', sans-serif;">
      <strong>אורחים:</strong> השימוש במערכת מוגבל.
      <a href="javascript:void(0);" onclick="document.getElementById('loginModal').style.display='flex'" style="color: #007bff; text-decoration: underline;">התחבר או הירשם</a>
      לגישה מלאה ולשמירת חליטות.
    </div>
    {% endif %}
    <div style="flex: 0 0 25%; border-bottom: 1px solid #ccc;">
      <form oninput="updateResults()">
        <!-- Line 1 -->
        <label for="cities"> </label>
        <div style="display:inline-flex; align-items:center;">
          <button id="open-cities-modal" type="button" class="toggle-button">ערים</button>
          <div id="selected-cities-display" style="font-weight:bold; margin-right: 5px;"></div>
        </div>
        <div id="cities-hidden-fields"></div> <!-- Container for hidden city inputs -->
        <label for="roaster"> </label>
        <div style="display:inline-flex; align-items:center;">
          <button id="open-roaster-modal" type="button" class="toggle-button">בית קליה</button>
          <div id="selected-roasters-display" style="font-weight:bold; margin-right: 5px;"></div>
        </div>
        <div id="roaster-hidden-fields"></div> <!-- Container for hidden roaster inputs -->
       <div style="display:inline-flex; align-items:center;">
          <label for="origin"> </label>
          <button id="open-origin-modal" type="button" class="toggle-button">ארץ מוצא</button>
          <div id="selected-origins-display" style="font-weight:bold; margin-right: 5px;"></div>
        </div>
        <div id="origin-hidden-fields"></div> <!-- Container for hidden origin inputs -->
        <label for="flavors">   </label>
        <div style="display:inline-flex; align-items:center;">
          <button id="open-flavors-modal" type="button" class="toggle-button">טעמים</button>
          <div id="selected-flavors-display" style="font-weight:bold; margin-right: 5px;"></div>
        </div>
        <div id="flavors-hidden-fields"></div>
        <div class="select-container">
          <label class="select-label" for="processing">עיבוד:</label>
          <select class="modern-select" name="processing" id="processing">
              <option value="">כל סוגי העיבוד</option>
              <option value="שטוף">שטוף</option>
              <option value="נטורל (יבש)">נטורל (יבש)</option>
              <option value="הוני פרוסס">הוני פרוסס</option>
              <option value="מיוחד">מיוחד</option>
          </select>
      </div>

        <!-- Line 2 -->
        <div class="select-container">
          <label class="select-label" for="roast_level">דרגת קלייה:</label>
          <select class="modern-select" name="roast_level"  id="roast_level">
              <option value="">כל דרגות הקלייה</option>
              <option value="בהירה">בהירה</option>
              <option value="בהירה-בינונית">בהירה-בינונית</option>
              <option value="בינונית">בינונית</option>
              <option value="בינונית-כהה">בינונית-כהה</option>
              <option value="כהה">כהה</option>
          </select>
      </div>
      <div class="select-container">
        <label class="select-label" for="elevation">גובה:</label>
        <select class="modern-select" name="elevation" id="elevation" onchange="updateResults()">
            <option value="">הכל</option>
            <option value="altd_1">0-1000</option>
            <option value="altd_2">1001-1500</option>
            <option value="altd_3">1501-2000</option>
            <option value="altd_4">-2001</option>
        </select>
    </div>
       <!--<br><br>-->
        <label for="arabica">   </label>
        <!-- Replaced checkbox with button -->
        <button type="button" id="arabicaBtn" class="toggle-button">ערביקה</button>
        <label for="robusta">    </label>
        <!-- Replaced checkbox with button -->
        <button type="button" id="robustaBtn" class="toggle-button">רובוסטה</button>
        <label for="singOBtn">    </label>
        <!-- Added button for single origin -->
        <button type="button" id="singOBtn" class="toggle-button">חד-זני</button>
        <label for="mixBtn">    </label>
        <button type="button" id="mixBtn" class="toggle-button">תערובת</button>

        <label for="specialityBtn">      </label>
        <button type="button" id="specialityBtn" class="toggle-button">Specialty Coffee</button>
        <label for="decafBtn">      </label>
        <button type="button" id="decafBtn" class="toggle-button">נטול קפאין</button>

        <!-- Brew Method Checkboxes and Reset Button Group -->
        <div style="display: inline-flex; align-items: center; vertical-align: middle; gap: 5px; margin-left: 15px;">
          <button type="button" onclick="resetSearch()" class="toggle-button reset-button" style="margin-left: 5px;">אפס חיפוש</button>
          <!-- Checkboxes without individual labels, using title for tooltip. Small margin for visual separation. -->
            <input type="checkbox" id="filterTurkish" class="brew-filter" value="turkish" checked onchange="updateResults()" title="טורקי" style="margin: 0 5px;">
            <input type="checkbox" id="filterEspresso" class="brew-filter" value="espresso" checked onchange="updateResults()" title="אספרסו" style="margin: 0 15px;">
            <input type="checkbox" id="filterFrenchPress" class="brew-filter" value="french_press" checked onchange="updateResults()" title="פרנץ' פרס" style="margin: 0 15px;">
            <input type="checkbox" id="filterPourOver" class="brew-filter" value="pour_over" checked onchange="updateResults()" title="פור אובר" style="margin: 0 12px;">
            <input type="checkbox" id="filterDrip" class="brew-filter" value="drip" checked onchange="updateResults()" title="דריפ" style="margin: 0 10px;">
            <input type="checkbox" id="filterColdBrew" class="brew-filter" value="cold_brew" checked onchange="updateResults()" title="קולד ברו" style="margin: 0 10px;">

            <!-- <button type="button" onclick="resetSearch()" class="toggle-button reset-button" style="margin-left: 5px;">אפס חיפוש</button> -->
        </div>
      </form>
    </div>
    <div id="bean-table-container" style="flex: 1; overflow-y: auto;">
      <table>
        <colgroup>
          <col style="width: 170px;" /> <!-- שם -->
          <col style="width: 120px;" /> <!-- קולה -->
          <col style="width: 160px;" /> <!-- ארץ מוצא -->
          <col style="width: 60px;" /> <!-- עיבוד -->
          <col style="width: 170px;" /> <!-- טעמים -->
          <col style="width: 40px;" />  <!-- דרגת קליה -->
          <col style="width: 50px;" /> <!-- גוף --> <!-- New column for body -->
          <col style="width: 50px;" /> <!-- חמיצות --> <!-- New column for acidity -->
          <col style="width: 50px;" />  <!-- ערביקה -->
          <col style="width: 50px;" />  <!-- רובוסטה -->
          <col style="width: 50px;" />  <!-- סוג -->
          <col style="width: 40px;" id="scaCol" /> <!-- New column for SCA Score -->
          <col style="width: 60px;" /> <!-- Speciality Coffee -->
          <col style="width: 50px;" />  <!-- מחיר משקל (decreased) -->
          <col style="width: 60px;" />  <!-- מחיר 100 גר (increased) -->
          <col style="width: 40px;" />  <!-- טורקי -->
          <col style="width: 50px;" />  <!-- אספרסו -->
          <col style="width: 40px;" />  <!-- פרנץ' פרס -->
          <col style="width: 40px;" />  <!-- פור אובר -->
          <col style="width: 40px;" />  <!-- דריפ -->
          <col style="width: 40px;" />  <!-- קולד ברו -->
          <col style="width: 30px;" />  <!-- Changed brew column from 40px to 30px -->
        </colgroup>
        <thead>
          <tr>
            <th class="title" onclick="sortBy('bean_name')" style="cursor: pointer;">
              שם
              <span id="bean_nameSortArrow"></span>
            </th>
            <th class="title" onclick="sortBy('roaster_name')" style="cursor: pointer;">
              בית קליה
              <span id="roaster_nameSortArrow"></span>
            </th>
            <th class="title">ארץ מוצא</th>
            <th class="title">עיבוד גובה</th>
            <th class="title">טעמים</th>
            <th class="title">דרגת קליה</th> <!-- Was דרגת קלייה -->
            <th class="title">גוף</th> <!-- New column for body -->
            <th class="title">חמיצות</th> <!-- New column for acidity -->
            <th class="title" onclick="sortBy('arabica')" style="cursor: pointer;">
              ערביקה
              <span id="arabicaSortArrow"></span>
            </th>
            <th class="title" onclick="sortBy('robusta')" style="cursor: pointer;">
              רובוסטה
              <span id="robustaSortArrow"></span>
            </th>
            <th class="title">סוג</th> <!-- Changed from תערובת to סוג -->
            <!-- We'll insert the SCA Score header dynamically -->
            <th class="title" id="specialityHeader">Specialty Coffee</th> <!-- Was קפה מיוחד -->
            <th class="title" onclick="sortBy('price')" style="cursor: pointer;">
              מחיר משקל
              <span id="priceSortArrow"></span>
            </th>
            <th class="title" onclick="sortBy('price100')" style="cursor: pointer;">
              מחיר 100 גר
              <span id="price100SortArrow"></span>
            </th>
            <th class="title">טורקי</th>
            <th class="title">אספרסו</th>
            <th class="title">פרנץ' פרס</th>
            <th class="title">פור אובר</th>
            <th class="title">דריפ</th>
            <th class="title">קולד ברו</th>
            <th class="title">חליטה</th>
          </tr>
        </thead>
        <tbody id="beanList">
          <!-- Results will be populated here -->
          {% for bean in beans %}
          <tr>
            <td>{{ bean.bean_name }}</td>
            <td>
              <span class="roaster-name" onclick="showRoasterDetails('{{ bean.roaster_id }}')">{{ bean.roaster_name }}</span>
            </td>
            <td>{{ bean.origin }}</td>
            <td>{{ bean.processing }}</td>
            <td>{{ bean.flavors }}</td>
            <td>{{ bean.roast_level }}</td>
            <td>{{ bean.body }}</td> <!-- New column for body -->
            <td>{{ bean.acidity }}</td> <!-- New column for acidity -->
            <td>{{ bean.arabica }}</td>
            <td>{{ bean.robusta }}</td>
            <td>
              {% if bean.mix %}
                תערובת
              {% else %}
                חד-זני
              {% endif %}
            </td>
            <td>{{ bean.speciality }}</td>
            <td>{{ bean.price }}</td>
            <td>{{ bean.weight }}</td>
            <td>{{ bean.turkish }}</td>
            <td>{{ bean.espresso }}</td>
            <td>{{ bean.french_press }}</td>
            <td>{{ bean.pour_over }}</td>
            <td>{{ bean.drip }}</td>
            <td>{{ bean.cold_brew }}</td>
            <td>
              <button class="brew-button">חליטה</button>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>

  <!-- Brew Modal in its own form -->
  <div id="brewModal" class="modal" style="display: none; width: 80%; max-width: 800px;">
    <div class="modal-content" style="position: relative; display: flex; flex-direction: column;">
      <span class="close-btn" style="position: absolute; left: 2px; top: 2px; cursor: pointer; font-size: 36px;" onclick="closeBrewModal()">&times;</span>
      <!-- Added Title -->
      <div class="modal-title" style="position: absolute; top: 10px; right: 30px; font-weight: bold; color: rgb(85, 32, 244); font-family: 'Varela Round', sans-serif;">
        רישום חליטה
      </div>
      <!-- New title line for bean name -->
      <div id="beanNameTitle" style="position: absolute; top: 40px; right: 30px; font-weight: normal; color: rgb(85,32,244); font-family: 'Varela Round', sans-serif;">
        <!-- Bean name will appear here -->
      </div>
      <form id="brewForm" style="display: flex; font-family: 'Varela Round', sans-serif; margin-top: 70px;">
        <div style="flex: 1; margin-right: 10px;">
          <!-- Previously right column (fields from bean_purchase_date to coffee_output) -->
          <input type="hidden" id="bean_id" name="bean_id">
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
          <div class="form-group">
            <label for="bean_purchase_date">תאריך רכישה:</label>
            <input type="text" id="bean_purchase_date" name="bean_purchase_date" style="width: 120px;" lang="he">
          </div>
          <div class="form-group">
            <label for="bean_roasting_date">תאריך קלייה:</label>
            <input type="text" id="bean_roasting_date" name="bean_roasting_date" style="width: 120px;" lang="he">
          </div>
          <div class="form-group">
            <label for="brew_method">שיטת חליטה:</label>
            <select id="brew_method" name="brew_method" style="width: 120px;">
              <option value="אספרסו">אספרסו</option>
              <option value="פילטר">פילטר</option>
              <option value="פרנץ פרס">פרנץ פרס</option>
            </select>
          </div>
          <label for="grind_settings">בורר טחינה:</label>
          <input type="text" id="grind_settings" name="grind_settings"  style="border: 0.5px solid black; width:60px;" oninput="validateNumeric(this)">
          <br> <br>
          <label for="brew_temp">(°C) טמפ חליטה:</label>
          <input type="text" id="brew_temp" name="brew_temp"  style="border: 0.5px solid black; width:60px;" oninput="validateNumeric(this)">
          <br> <br>
          <label for="PreInfusionTimeSec">פרה-אינפיוזן (שניות):</label>
          <input type="text" id="PreInfusionTimeSec" name="PreInfusionTimeSec" style="border: 0.5px solid black; width:60px;" oninput="validateNumeric(this)">
          <br> <br>
          <label for="coffee_dose">כמות קפה (גרם):</label>
          <input type="text" id="coffee_dose" name="coffee_dose"  style="border: 0.5px solid black; width:60px;" oninput="validateNumeric(this)">
          <br> <br>
          <label for="brew_time">זמן חליטה (שניות:דקות):</label>
          <input type="text" id="brew_time" name="brew_time" style="border: 0.5px solid black; width:80px;">
          <br> <br>
          <label for="coffee_output">תפוקת קפה (גרם):</label>
          <input type="text" id="coffee_output" name="coffee_output"  style="border: 0.5px solid black; width:40px;" oninput="validateNumeric(this)">
        </div>
        <div style="flex: 1;">
          <!-- Previously left column (fields not from bean_purchase_date to coffee_output) -->
          <div class="form-group">
            <label for="aroma">ארומה:</label>
            <textarea id="aroma" name="aroma" rows="2" style="border: 0.5px solid black;"></textarea>
          </div>
          <div class="form-group">
            <label for="acidity">חמיצות:</label>
            <textarea id="acidity" name="acidity" rows="2" style="border: 0.5px solid black;"></textarea>
          </div>
          <div class="form-group">
            <label for="sweetness">מתיקות:</label>
            <textarea id="sweetness" name="sweetness" rows="2" style="border: 0.5px solid black;"></textarea>
          </div>
          <div class="form-group">
            <label for="bitterness">מרירות:</label>
            <textarea id="bitterness" name="bitterness" rows="2" style="border: 0.5px solid black;"></textarea>
          </div>
          <div class="form-group">
            <label for="body">גוף:</label>
            <select id="body" name="body" style="width: 60px;">
              <option value="קל">קל</option>
              <option value="בינוני">בינוני</option>
              <option value="מלא">מלא</option>
            </select>
          </div>
          <div class="form-group">
            <label for="aftertaste">שארית טעם:</label>
            <textarea id="aftertaste" name="aftertaste" rows="2" style="border: 0.5px solid black;"></textarea>
          </div>
          <div class="form-group">
            <label for="overall_rating">ניקוד:</label>
            <select id="overall_rating" name="overall_rating" style="width: 60px;">
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
              <option value="4">4</option>
              <option value="5">5</option>
            </select>
          </div>
          <button type="submit" class="save-button">שמור</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Move the modal container here -->
  <div class="modal-container">
    <div id="roaster-modal" class="roaster-modal" style="display: none;">
      <div class="roaster-modal-content" data-title="בחירת בית קליה">
        <span id="close-roaster-modal" class="close-btn" style="font-size: 24px; cursor: pointer;">&times;</span>
        <div id="roaster-hidden-fields"></div>
        <div id="selected-roasters-display"></div>
        <div class="roaster-grid">
          {% for roaster in roasters %}
            <div class="roaster-option" data-roaster-id="{{ roaster.roaster_id }}" data-roaster-name="{{ roaster.name }}">
              {{ roaster.name }}
            </div>
          {% endfor %}
          <!-- ...existing code, if any... -->
        </div>
      </div>
    </div>

    <div id="origin-modal" class="origin-modal" style="display: none;">
      <div class="origin-modal-content" data-title="בחירת ארץ מוצא">
        <span id="close-origin-modal" class="close-btn" style="font-size: 24px; cursor: pointer;">&times;</span>
        <div class="origin-grid">
          <!-- Display countries in a 4-column layout -->
          {% for country in countries %}
          {% if user_logged_in or loop.index <= 10 %}
          <div class="origin-option" data-country="{{country.country}}">{{country.country}}</div>
          {% endif %}
          {% endfor %}
        </div>
      </div>
    </div>

    <div id="flavors-modal" class="flavors-modal" style="display: none;">
      <div class="origin-modal-content" data-title="בחירת טעמים">
        <span id="close-flavors-modal" class="close-btn" style="font-size: 24px; cursor: pointer;">&times;</span>
        <!-- Moved flavor mode toggle button to the left side -->
        <div style="text-align: left; margin-bottom: 10px;">
          <button id="toggleFlavorMode" type="button" class="toggle-button">OR :התאמת חיפוש</button>
        </div>
        <div class="originb-grid">
          {% for f in flavors %}
          {% if user_logged_in or loop.index <= 10 %}
          <div class="flavor-option" data-flavor="{{ f.c_flavor }}">{{ f.c_flavor }}</div>
          {% endif %}
          {% endfor %}
        </div>
      </div>
    </div>

    <div id="cities-modal" class="origin-modal" style="display: none;">
      <div class="origin-modal-content" data-title="בחירת ערים">
        <span id="close-cities-modal" class="close-btn" style="font-size: 24px; cursor: pointer;">&times;</span>
        <div class="origin-grid">
          <!-- Display cities in a 4-column layout -->
          {% for city in cities %}
          {% if user_logged_in or loop.index <= 10 %}
          <div class="city-option" data-city="{{city.c_city}}">{{city.c_city}}</div>
          {% endif %}
          {% endfor %}
        </div>
      </div>
    </div>
  </div>

  <div id="roasterDetailModal" class="modal" style="display:none;">
    <div class="modal-content">
      <span class="close-btn" onclick="closeRoasterModal()">&times;</span>
      <div id="roasterDetailContent"></div>
    </div>
  </div>

  <script>
    // Helper function to get cookie by name
    function getCookie(name) {
      let cookieValue = null;
      if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i].trim();
          // Does this cookie string begin with the name we want?
          if (cookie.substring(0, name.length + 1) === (name + '=')) {
            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
            break;
          }
        }
      }
      return cookieValue;
    }

    var selectedRoasters = [];  // Moved to global scope

    document.addEventListener('DOMContentLoaded', function() {
        const arabicaBtn = document.getElementById('arabicaBtn');
        const robustaBtn = document.getElementById('robustaBtn');
        const singOBtn = document.getElementById('singOBtn'); // Added Single Origin button reference
        const mixBtn = document.getElementById('mixBtn'); // Added Mix button reference
        const specialityBtn = document.getElementById('specialityBtn'); // Added Speciality button reference
        const decafBtn = document.getElementById('decafBtn'); // Added Decaf button reference

        // Ensure buttons are inactive on page load
        arabicaBtn.classList.remove('active');
        robustaBtn.classList.remove('active');
        singOBtn.classList.remove('active'); // Initialize Single Origin button
        mixBtn.classList.remove('active'); // Initialize Mix button
        specialityBtn.classList.remove('active'); // Initialize Speciality button
        decafBtn.classList.remove('active'); // Initialize Decaf button

        arabicaBtn.addEventListener('click', function() {
            this.classList.toggle('active');
            updateResults(); // Update results on toggle
        });
        robustaBtn.addEventListener('click', function() {
            this.classList.toggle('active');
            updateResults(); // Update results on toggle
        });
        singOBtn.addEventListener('click', function() { // Added event listener for Single Origin button
            this.classList.toggle('active');
            updateResults(); // Update results on toggle
        });
        mixBtn.addEventListener('click', function() { // Added event listener for Mix button
            this.classList.toggle('active');
            updateResults(); // Update results on toggle
        });
        specialityBtn.addEventListener('click', function() { // Added event listener for Speciality button
            this.classList.toggle('active');
            updateResults(); // Update results on toggle
        });
        decafBtn.addEventListener('click', function() { // Added event listener for Decaf button
            this.classList.toggle('active');
            updateResults(); // Update results on toggle
        });

        let currentColorIndex = 0;
        const colorCycle = ['red','green','blue','silver'];
        const openRoasterModalBtn = document.getElementById('open-roaster-modal');
        const roasterModal = document.getElementById('roaster-modal');

        const closeRoasterModalBtn = document.getElementById('close-roaster-modal');
        const roasterHiddenFieldsContainer = document.getElementById('roaster-hidden-fields');
        const selectedRoastersDisplay = document.getElementById('selected-roasters-display');

        openRoasterModalBtn.addEventListener('click', function(e) {
          e.preventDefault();
          // Hide the origin modal if it is open
          document.getElementById('origin-modal').style.display = 'none';
          roasterModal.style.display = 'block';
        });

        closeRoasterModalBtn.addEventListener('click', () => {
          roasterModal.style.display = 'none';
        });

        document.querySelectorAll('.roaster-option').forEach(option => {
          option.addEventListener('click', function() {
            const roasterId = this.dataset.roasterId;
            const roasterName = this.dataset.roasterName;
            if (selectedRoasters.some(r => r.id === roasterId)) {
              selectedRoasters = selectedRoasters.filter(r => r.id !== roasterId);
              this.style.backgroundColor = '';
              this.style.color = '';

       const hiddenInput = document.getElementById('roaster-hidden-' + roasterId);
              if (hiddenInput) hiddenInput.remove();
            } else {
              if (selectedRoasters.length >= 3) return;
              selectedRoasters.push({ id: roasterId, name: roasterName });
              this.style.backgroundColor = colorCycle[currentColorIndex % colorCycle.length];
              this.style.color = '#fff';
              currentColorIndex++;

              const input = document.createElement('input');
              input.type = 'hidden';
              input.name = 'roasters';
              input.value = roasterId;
              input.id = 'roaster-hidden-' + roasterId;
              roasterHiddenFieldsContainer.appendChild(input);
            }
            updateResults();
            selectedRoastersDisplay.textContent = selectedRoasters.map(r => r.name).join(', ');
          });
        });
    });

    function resetSearch() {
        document.querySelector('form').reset();
        document.getElementById('arabicaBtn').classList.remove('active');
        document.getElementById('robustaBtn').classList.remove('active');
        document.getElementById('singOBtn').classList.remove('active'); // Reset Single Origin button
        document.getElementById('mixBtn').classList.remove('active'); // Reset Mix button
        document.getElementById('specialityBtn').classList.remove('active'); // Reset Speciality button
        document.getElementById('decafBtn').classList.remove('active'); // Reset Decaf button
        
        // Reset Israeli content button if it exists
        const israBtn = document.getElementById('israBtn');
        if (israBtn) israBtn.classList.remove('active');

        // Reset origin, flavors, and cities selections
        selectedOrigins = [];
        selectedFlavors = [];
        selectedCities = [];
        selectedRoastersByCity = [];
        document.getElementById('selected-origins-display').textContent = '';
        document.getElementById('selected-flavors-display').textContent = '';
        document.getElementById('selected-cities-display').textContent = '';
        const originHidden = document.getElementById('origin-hidden-fields');
        const flavorsHidden = document.getElementById('flavors-hidden-fields');
        const citiesHidden = document.getElementById('cities-hidden-fields');
        if (originHidden) originHidden.innerHTML = '';
        if (flavorsHidden) flavorsHidden.innerHTML = '';
        if (citiesHidden) flavorsHidden.innerHTML = '';
        document.querySelectorAll('.origin-option').forEach(option => {
            option.style.backgroundColor = '';
            option.style.color = '';
        });
        document.querySelectorAll('.flavor-option').forEach(option => {
            option.style.backgroundColor = '';
            option.style.color = '';
        });
        document.querySelectorAll('.city-option').forEach(option => {
            option.style.backgroundColor = '';
            option.style.color = '';
        });

        // Reset roaster selections
        selectedRoasters = [];
        const selectedRoastersDisplay = document.getElementById('selected-roasters-display');
        if (selectedRoastersDisplay) selectedRoastersDisplay.textContent = '';
        const roasterHiddenFieldsContainer = document.getElementById('roaster-hidden-fields');
        if (roasterHiddenFieldsContainer) roasterHiddenFieldsContainer.innerHTML = '';
        document.querySelectorAll('.roaster-option').forEach(option => {
            option.style.backgroundColor = '';
            option.style.color = '';
        });

        // Clear bean list so no beans are displayed after reset
        const beanList = document.getElementById('beanList');
        if (beanList) beanList.innerHTML = '';
        
        // Reset pagination state and remove load more button
        if (typeof currentPage !== 'undefined') currentPage = 1;
        if (typeof currentFilters !== 'undefined') currentFilters = null;
        if (typeof totalResults !== 'undefined') totalResults = 0;
        if (typeof removeLoadMoreButton === 'function') removeLoadMoreButton();
        
        // Reset sorting state
        if (typeof currentSort !== 'undefined') {
            currentSort = { field: null, direction: 'asc' };
        }
        updateSortArrows();
    }

    // Limit of 3 selections.
    const colorCycle = ['red','green','blue','silver'];
    let selectedOrigins = [];
    let currentColorIndex = 0;

    document.getElementById('open-origin-modal').addEventListener('click', function(e) {
      e.preventDefault();
      document.getElementById('origin-modal').style.display = 'block';
    });

    document.getElementById('close-origin-modal').addEventListener('click', () => {
      document.getElementById('origin-modal').style.display = 'none';
    });

    const hiddenFieldsContainer = document.getElementById('origin-hidden-fields');

    document.querySelectorAll('.origin-option').forEach(option => {
      option.addEventListener('click', function() {
        const country = this.dataset.country;
        if (selectedOrigins.includes(country)) {
          // Deselect
          selectedOrigins = selectedOrigins.filter(item => item !== country);
          this.style.backgroundColor = '';
          this.style.color = '';
          const hiddenInput = document.getElementById('origin-hidden-' + country);
          if (hiddenInput) hiddenInput.remove();
        } else {
          if (selectedOrigins.length >= 3) return;
          selectedOrigins.push(country);
          this.style.backgroundColor = colorCycle[currentColorIndex % colorCycle.length];
          this.style.color = '#fff';
          currentColorIndex++;

          // Create a hidden input for form submission
          const input = document.createElement('input');
          input.type = 'hidden';
          input.name = 'origin';
          input.value = country;
          input.id = 'origin-hidden-' + country;
          hiddenFieldsContainer.appendChild(input);
        }
        // After adjusting origins, update results & display
        updateResults();
        document.getElementById('selected-origins-display').textContent = selectedOrigins.join(', ');
      });
    });

    let selectedFlavors = [];
    document.getElementById('open-flavors-modal').addEventListener('click', function(e) {
      e.preventDefault();
      document.getElementById('flavors-modal').style.display = 'block';
    });
    document.getElementById('close-flavors-modal').addEventListener('click', () => {
      document.getElementById('flavors-modal').style.display = 'none';
    });
    document.querySelectorAll('.flavor-option').forEach(option => {
      option.addEventListener('click', function() {
        const flavor = this.dataset.flavor;
        if (selectedFlavors.includes(flavor)) {
          selectedFlavors = selectedFlavors.filter(item => item !== flavor);
          this.style.backgroundColor = '';
          this.style.color = '';
          const hiddenInput = document.getElementById('flavor-hidden-' + flavor);
          if (hiddenInput) hiddenInput.remove();
        } else {
          if (selectedFlavors.length >= 3) return;
          selectedFlavors.push(flavor);
          this.style.backgroundColor = colorCycle[currentColorIndex % colorCycle.length];
          this.style.color = '#fff';
          currentColorIndex++;
          const input = document.createElement('input');
          input.type = 'hidden';
          input.name = 'flavors';
          input.value = flavor;
          input.id = 'flavor-hidden-' + flavor;
          document.getElementById('flavors-hidden-fields').appendChild(input);
        }
        updateResults();
        document.getElementById('selected-flavors-display').textContent = selectedFlavors.join(', ');
      });
    });

    // City selection functionality
    let selectedCities = [];
    let selectedRoastersByCity = [];

    document.getElementById('open-cities-modal').addEventListener('click', function(e) {
      e.preventDefault();
      document.getElementById('cities-modal').style.display = 'block';
    });

    document.getElementById('close-cities-modal').addEventListener('click', () => {
      document.getElementById('cities-modal').style.display = 'none';
    });

    document.addEventListener('DOMContentLoaded', function() {
      document.querySelectorAll('.city-option').forEach(option => {
        option.addEventListener('click', function() {
          const city = this.dataset.city;
          if (selectedCities.includes(city)) {
            // Deselect
            selectedCities = selectedCities.filter(item => item !== city);
            this.style.backgroundColor = '';
            this.style.color = '';

            // Remove from hidden fields
            const hiddenInput = document.getElementById('city-hidden-' + city);
            if (hiddenInput) hiddenInput.remove();
          } else {
            if (selectedCities.length >= 3) return;
            selectedCities.push(city);
            this.style.backgroundColor = colorCycle[currentColorIndex % colorCycle.length];
            this.style.color = '#fff';
            currentColorIndex++;

            // Add to hidden fields
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'cities';
            input.value = city;
            input.id = 'city-hidden-' + city;
            document.getElementById('cities-hidden-fields').appendChild(input);

            // Fetch roasters for this city
            fetchRoastersForCity(city);
          }

          document.getElementById('selected-cities-display').textContent = selectedCities.join(', ');
        });
      });
    });

    function fetchRoastersForCity(city) {
      // Get CSRF token
      let csrfToken = null;
      const metaToken = document.querySelector('meta[name="csrf-token"]');
      if (metaToken) {
        csrfToken = metaToken.getAttribute('content');
      }
      if (!csrfToken) {
        csrfToken = getCookie('csrftoken') || getCookie('X-CSRFToken') || getCookie('_csrf_token');
      }

      fetch('/get_roasters_by_city', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({ city: city })
      })
      .then(response => {
        if (!response.ok) {
          return response.text().then(text => {
            throw new Error(`Server error: ${response.status} - ${text}`);
          });
        }
        return response.json();
      })
      .then(data => {
        if (data.roasters && data.roasters.length > 0) {
          // Store the roasters for this city
          selectedRoastersByCity = selectedRoastersByCity.concat(data.roasters);

          // Update the global selectedRoasters array with these roasters
          data.roasters.forEach(roaster => {
            if (!selectedRoasters.some(r => r.id === roaster.id)) {
              selectedRoasters.push(roaster);
            }
          });

          // Update the display
          document.getElementById('selected-roasters-display').textContent =
            selectedRoasters.map(r => r.name).join(', ');

          // Update the hidden fields for roasters
          const roasterHiddenFieldsContainer = document.getElementById('roaster-hidden-fields');
          roasterHiddenFieldsContainer.innerHTML = '';
          selectedRoasters.forEach(roaster => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'roasters';
            input.value = roaster.id;
            input.id = 'roaster-hidden-' + roaster.id;
            roasterHiddenFieldsContainer.appendChild(input);
          });

          // Update the results
          updateResults();
        }
      })
      .catch(error => {
        alert(`שגיאה בטעינת בתי קלייה: ${error.message}`);
      });
    }

    // This function has been moved to update_results.js
    // Keeping a reference here for backward compatibility
    function updateResults() {
        // Call the implementation from the external JS file
        if (typeof window.externalUpdateResults === 'function') {
            window.externalUpdateResults();
        }
    }

    function showRoasterDetails(roasterId) {
    const content = document.getElementById('roasterDetailContent');
    content.innerHTML = 'טוען...';
    document.getElementById('roasterDetailModal').style.display = 'block';

    fetch(`/get_roaster_details/${roasterId}`)
        .then(response => {
            if (!response.ok) {
                // If we get a 401 (Unauthorized) or 403 (Forbidden), it means the user is not logged in
                if (response.status === 401 || response.status === 403) {
                    throw new Error('יש להתחבר למערכת כדי לצפות בפרטי הקולה המלאים');
                }
                return response.text().then(text => {
                    throw new Error(`Server error: ${response.status} - ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            if (data && data.name) {
                content.innerHTML = `
                    <h2>${data.name}</h2>
                    <p><strong>כתובת:</strong> ${data.address || 'לא זמין'}</p>
                    <p><strong>עיר:</strong> ${data.city || 'לא זמין'}</p>
                    <p><strong>מיקוד:</strong> ${data.zip || 'לא זמין'}</p>
                    <p><strong>אתר:</strong> ${data.website ? `<a href="${data.website}" target="_blank">אתר</a>` : 'לא זמין'}</p>
                    <p><strong>דוא"ל:</strong> ${data.email || 'לא זמין'}</p>
                    <p><strong>משלוח חינם מעל:</strong> ${data.minimum_shipping || 'לא זמין'}</p>
                    <p><strong>דמי משלוח:</strong> ${data.shipping_cost || 'לא זמין'}</p>
                `;
            } else {
                content.innerHTML = `<p style="color: red;">לא נמצאו פרטים עבור קולה זו.</p>`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            content.innerHTML = `<p style="color: red;">שגיאה בטעינת פרטי הקולה: ${error.message}</p>`;
        });
}

    function closeRoasterModal() {
      document.getElementById('roasterDetailModal').style.display = 'none';
    }

    // Close modal when clicking outside
    window.onclick = function(event) {
      const modal = document.getElementById('roasterDetailModal');
      if (event.target == modal) {
        modal.style.display = 'none';
      }
    }

  </script>
<!-- Load the external JS file that defines updateResults with fallback -->
<script>
  // Try to load the script from the static folder
  function loadScript(src, fallbackSrc, callback) {
    var script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = src;
    script.onerror = function() {
      console.error('Failed to load script from:', src);
      console.log('Trying fallback source:', fallbackSrc);
      var fallbackScript = document.createElement('script');
      fallbackScript.type = 'text/javascript';
      fallbackScript.src = fallbackSrc;
      fallbackScript.onerror = function() {
        console.error('Failed to load script from fallback source:', fallbackSrc);
      };
      fallbackScript.onload = callback;
      document.head.appendChild(fallbackScript);
    };
    script.onload = callback;
    document.head.appendChild(script);
  }

  // Load the script with fallback
  loadScript(
    "{{ url_for('static', filename='js/update_results.js') }}",
    "/fallback/update_results.js",
    function() {
      // Script loaded successfully
    }
  );
</script>
<!-- Fallback in case the external file fails to load -->
<script>
  // Check if the external updateResults function was loaded
  window.addEventListener('load', function() {
    if (typeof window.externalUpdateResults !== 'function') {

      // Define the fallback function
      window.externalUpdateResults = async function() {

        const formData = new FormData(document.querySelector('form'));
        const data = Object.fromEntries(formData.entries());
        
        // Use global arrays directly instead of form data to ensure proper deselection handling
        data.origin = typeof selectedOrigins !== 'undefined' ? selectedOrigins : [];
        data.flavors = typeof selectedFlavors !== 'undefined' ? selectedFlavors : [];

        // Ensure arrays are not empty strings
        if (typeof data.origin === 'string' && data.origin === '') data.origin = [];
        if (typeof data.flavors === 'string' && data.flavors === '') data.flavors = [];

        // Get state from buttons
        const arabicaBtn = document.getElementById('arabicaBtn');
        const robustaBtn = document.getElementById('robustaBtn');
        const singOBtn = document.getElementById('singOBtn');
        const mixBtn = document.getElementById('mixBtn');
        const specialityBtn = document.getElementById('specialityBtn');
        const decafBtn = document.getElementById('decafBtn');
        const israBtn = document.getElementById('israBtn'); // Israeli content button (may not exist)

        data.arabica = arabicaBtn && arabicaBtn.classList.contains('active') ? 1 : 0;
        data.robusta = robustaBtn && robustaBtn.classList.contains('active') ? 1 : 0;
        data.singleorigin = singOBtn && singOBtn.classList.contains('active') ? 1 : 0;
        data.mix = mixBtn && mixBtn.classList.contains('active') ? 1 : 0;
        data.speciality = specialityBtn && specialityBtn.classList.contains('active') ? 1 : 0;
        data.decaf = decafBtn && decafBtn.classList.contains('active') ? 1 : 0;
        data.isra_content = israBtn && israBtn.classList.contains('active') ? 1 : 0;
        
        // Get elevation value
        const elevationSelect = document.getElementById('elevation');
        data.elevation = elevationSelect && elevationSelect.value ? [elevationSelect.value] : [];

        // Get processing value - clear if empty
        const processingSelect = document.getElementById('processing');
        data.processing = processingSelect && processingSelect.value ? processingSelect.value : '';

        // Get roast level value - clear if empty  
        const roastLevelSelect = document.getElementById('roast_level');
        data.roast_level = roastLevelSelect && roastLevelSelect.value ? roastLevelSelect.value : '';

        // Include cities from global selectedCities array
        data.cities = typeof selectedCities !== 'undefined' ? selectedCities : [];
        
        // Collect brew methods from checkboxes
        const brewFilters = document.querySelectorAll('.brew-filter:checked');
        data.brew_methods = Array.from(brewFilters).map(cb => cb.value);
        
        // Check if all brew methods are selected (which is the same as no filter)
        const allBrewFilters = document.querySelectorAll('.brew-filter');
        const allBrewMethodsSelected = brewFilters.length === allBrewFilters.length;
        
        // Ensure all required fields have proper defaults
        data.page = 1;
        data.per_page = 20;
        data.roasters = typeof selectedRoasters !== 'undefined' ? selectedRoasters.map(r => r.id) : [];

        // If no filters are applied, clear table and skip fetch
        const beanList = document.getElementById('beanList');
        if (
            (!data.origin || data.origin.length === 0) &&
            (!data.flavors || data.flavors.length === 0) &&
            (!data.processing || data.processing === '') &&
            (!data.roast_level || data.roast_level === '') &&
            (!data.elevation || data.elevation.length === 0) &&
            data.arabica === 0 &&
            data.robusta === 0 &&
            data.singleorigin === 0 &&
            data.mix === 0 &&
            data.speciality === 0 &&
            data.decaf === 0 &&
            (allBrewMethodsSelected || !data.brew_methods || data.brew_methods.length === 0) &&
            (!data.roasters || data.roasters.length === 0) &&
            (!data.cities || data.cities.length === 0) &&
            data.isra_content === 0
        ) {
            if (beanList) {
                beanList.innerHTML = ''; // Clear table
            }
            return; // Skip fetching
        }

        // Get CSRF token
        let csrfToken = null;
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) {
          csrfToken = metaToken.getAttribute('content');
        }

        // Get API key from meta tag
        const apiKeyMeta = document.querySelector('meta[name="api-key"]');
        const apiKey = apiKeyMeta ? apiKeyMeta.getAttribute('content') : 'demo-key-12345-67890-abcdef';

        try {
          const response = await fetch('/internal/search_beans', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRFToken': csrfToken
            },
            body: JSON.stringify(data)
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const beans = await response.json();

          const beanList = document.getElementById('beanList');
          if (!beanList) return;

          if (beans.length === 0) {
            beanList.innerHTML = '<tr><td colspan="21">לא נמצאו פולים התואמים לסינון המבוקש.</td></tr>';
            return;
          }

          // Simple display function
          beanList.innerHTML = '';
          beans.forEach(bean => {
            const row = document.createElement('tr');
            row.innerHTML = `
              <td>${bean.bean_name || ''}</td>
              <td>${bean.roaster_name || ''}</td>
              <td>${bean.origin || ''}</td>
              <td>${bean.processing || ''}<br>${bean.elevation || ''}</td>
              <td>${bean.flavors || ''}</td>
              <td>${bean.roast_level || ''}</td>
              <td>${bean.body || ''}</td>
              <td>${bean.acidity || ''}</td>
              <td>${bean.arabica || ''}</td>
              <td>${bean.robusta || ''}</td>
              <td>${bean.mix ? 'תערובת' : 'חד-זני'}</td>
              <td>${(bean.speciality === 1 || bean.speciality === true) ? '✓' : ''}</td>
              <td>${bean.SCA_score || ''}</td>
              <td>${(bean.price || '')}<br>${bean.weight == 1000 ? "1 ק\"ג" : (bean.weight || '')}</td>
              <td>${bean.price && bean.weight ? (bean.price / (bean.weight / 100)).toFixed(2) : ""}</td>
              <td>${bean.turkish ? '✓' : ''}</td>
              <td>${bean.espresso ? '✓' : ''}</td>
              <td>${bean.french_press ? '✓' : ''}</td>
              <td>${bean.pour_over ? '✓' : ''}</td>
              <td>${bean.drip ? '✓' : ''}</td>
              <td>${bean.cold_brew ? '✓' : ''}</td>
              <td>
                {% if user_logged_in %}
                  <button class="brew-button" onclick="openBrewModal(${bean.bean_id}, '${bean.bean_name}')">חליטה</button>
                {% else %}
                  <span style="color: #666; font-size: 12px; cursor: pointer;" onclick="document.getElementById('loginModal').style.display='flex'">התחבר לשמירה</span>
                {% endif %}
              </td>
            `;
            beanList.appendChild(row);
          });
        } catch (error) {
          const beanList = document.getElementById('beanList');
          if (beanList) {
            beanList.innerHTML = `<tr><td colspan="21">שגיאה בטעינת הפולים: ${error.message}</td></tr>`;
          }
        }
      };

      // Define the updateResults function if it doesn't exist
      if (typeof window.updateResults !== 'function') {
        window.updateResults = function() {
          window.externalUpdateResults();
        };
      }
    }
  });
</script>
{% if show_login_modal %}
<script>
  document.addEventListener('DOMContentLoaded', function(){
    var loginModal = document.getElementById('loginModal');
    if (loginModal) {
      loginModal.style.display = 'flex';
    }
  });
</script>
{% endif %}
{% endblock %}