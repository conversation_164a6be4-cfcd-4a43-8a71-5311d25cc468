{% extends "base.html" %}
{% block title %}החליטות שלי{% endblock %}
{% block content %}
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>החליטות שלי</title>
  <style>
    body {
        font-family: 'Varela Round', sans-serif;
        font-size: 14px;
    }
    table {
        width: 100%;
        font-family: 'Varela Round', sans-serif;
        font-size: 14px;
        border-collapse: collapse;
    }
    th.title {
        background-color: darkblue;
        color: white;
        position: sticky;
        top: 0;
        z-index: 1;
    }
    tr:nth-child(even) {
        background-color: #ddeeff;
    }
    tr:nth-child(odd) {
        background-color: white;
    }
    th, td {
        line-height: 2em;
        height: 4em;
        overflow: hidden;
        text-overflow: ellipsis;
        border: 1px solid #ddd;
        padding: 4px 8px;
    }
    .centered {
        text-align: center;
    }
    .page-title {
        text-align: center;
        margin: 20px 0;
        font-size: 24px;
        color: #333;
    }
    .no-records {
        text-align: center;
        margin: 50px 0;
        color: #666;
    }
    /* Ratings color coding */
    .rating-1 { color: #d32f2f; } /* Red */
    .rating-2 { color: #ff5722; } /* Orange-Red */
    .rating-3 { color: #ff9800; } /* Orange */
    .rating-4 { color: #4caf50; } /* Green */
    .rating-5 { color: #2e7d32; } /* Dark Green */
    
    /* Add styles for the delete button */
    .delete-btn {
      background-color: #ff3333;
      color: white;
      border: none;
      border-radius: 3px;
      padding: 3px 8px;
      cursor: pointer;
      font-size: 12px;
    }
    .delete-btn:hover {
      background-color: #cc0000;
    }
    
    /* Add a style for the actions column */
    .actions-col {
      width: 60px;
      text-align: center;
    }
    
    /* Add styles for the confirmation modal */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgba(0,0,0,0.4);
    }
    
    .modal-content {
      background-color: #fefefe;
      margin: 15% auto;
      padding: 20px;
      border: 1px solid #888;
      width: 300px;
      text-align: center;
      border-radius: 5px;
    }
    
    .modal-buttons {
      margin-top: 15px;
    }
    
    .modal-buttons button {
      margin: 0 10px;
      padding: 5px 15px;
      cursor: pointer;
    }
    
    #confirmDelete {
      background-color: #ff3333;
      color: white;
      border: none;
    }
    
    #cancelDelete {
      background-color: #ddd;
      border: none;
    }
    
    /* Add mobile-specific styles for fixed header and scrollable table */
    @media (max-width: 640px) {
      /* Container for the brew logs section */
      .brew-logs-container {
        display: flex;
        flex-direction: column;
        height: 100vh; /* Use full viewport height */
        padding-bottom: 10px;
      }
      
      /* Fix the header at the top */
      .brew-logs-header {
        flex: 0 0 auto; /* Don't grow or shrink */
        padding-bottom: 10px;
        position: sticky;
        top: 0;
        background-color: white; /* Ensure header has a background */
        z-index: 10;
      }
      
      /* Allow only the table container to scroll */
      .brew-logs-table-container {
        flex: 1; /* Take remaining space */
        overflow-y: auto; /* Enable vertical scrolling */
        -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
      }
      
      /* Ensure the table takes full width */
      .brew-logs-table {
        width: 100%;
      }
      
      /* Make the table header sticky */
      .brew-logs-table thead th {
        position: sticky;
        top: 0;
        z-index: 5;
        background-color: darkblue;
        color: white;
      }
      
      /* Apply high-contrast mode styles when needed */
      body.high-contrast .brew-logs-header {
        background-color: black;
      }
      
      body.high-contrast .brew-logs-table thead th {
        background-color: black;
        border-bottom: 1px solid white;
      }
      
      /* Apply light background mode styles when needed */
      body.light-background .brew-logs-header {
        background-color: white;
      }
      
      body.light-background .brew-logs-table thead th {
        background-color: #f8f8f8;
        border-bottom: 1px solid #ccc;
      }

      /* Hardcoded column widths for each column in mobile view */
      .brew-logs-table col:nth-child(1) { width: 120px; }
      .brew-logs-table col:nth-child(2) { width: 120px; }
      .brew-logs-table col:nth-child(3) { width: 120px; }
      .brew-logs-table col:nth-child(4) { width: 100px; }
      .brew-logs-table col:nth-child(5) { width: 120px; }
      .brew-logs-table col:nth-child(6) { width: 120px; }
      .brew-logs-table col:nth-child(7) { width: 70px; }
      .brew-logs-table col:nth-child(8) { width: 70px; }
      .brew-logs-table col:nth-child(9) { width: 80px; }
      .brew-logs-table col:nth-child(10) { width: 80px; }
      .brew-logs-table col:nth-child(11) { width: 70px; }
      .brew-logs-table col:nth-child(12) { width: 70px; }
      .brew-logs-table col:nth-child(13) { width: 70px; }
      .brew-logs-table col:nth-child(14) { width: 60px; }
      .brew-logs-table col:nth-child(15) { width: 100px; }
      .brew-logs-table col:nth-child(16) { width: 100px; }
      .brew-logs-table col:nth-child(17) { width: 50px; }
      .brew-logs-table col:nth-child(18) { width: 50px; }
    }
  </style>
  <!-- <script>
    // Add logging to help debug template rendering
    console.log("Loading showBrew.html template");
    {% if brewlogs %}
      console.log("Number of brewlogs received: {{ brewlogs|length }}");
    {% else %}
      console.log("No brewlogs received");
    {% endif %}
  </script> -->
</head>
<body class="showbrew" dir="rtl">
  <!-- Wrap the entire content in the brew-logs-container -->
  <div class="brew-logs-container">
    <!-- Header section stays fixed -->
    <div class="brew-logs-header">
      <h1 class="page-title">החליטות שלי</h1>
      
      <!-- Filter controls -->
      <div class="filter-controls">
        <!-- ...existing filter controls... -->
      </div>
    </div>
    
    <!-- Table container that will scroll -->
    <div class="brew-logs-table-container">
      {% if error %}
      <div class="error-message">
        שגיאה בטעינת החליטות: {{ error }}
      </div>
      {% endif %}
    
      {% if brewlogs and brewlogs|length > 0 %}
        <table class="brew-logs-table">
          <colgroup>
            <col style="width: 90px;" /> <!-- תאריך -->
            <col style="width: 150px;" /> <!-- שם הקפה -->
            <col style="width: 130px;" /> <!-- בית קלייה -->
            <col style="width: 130px;" /> <!-- ארץ מוצא -->
            <col style="width: 90px;" /> <!--  תאריך קליה -->
            <col style="width: 90px;" /> <!-- תאריך רכישה -->
            <col style="width: 40px;" /> <!-- שיטת חליטה -->
            <col style="width: 40px;" /> <!-- בורר טחינה -->
            <col style="width: 40px;" /> <!-- טמפ' חליטה -->
            <col style="width: 40px;" /> <!-- פרה אינפיוזן -->
            <col style="width: 50px;" /> <!-- כמות קפה -->
            <col style="width: 50px;" /> <!-- זמן -->
            <col style="width: 50px;" /> <!-- תפוקת קפה -->
            <col style="width: 40px;" /> <!-- גוף -->
            <col style="width: 150px;" /> <!-- ארומה -->
            <col style="width: 250px;" /> <!-- טעמים -->
            <col style="width: 30px;" /> <!-- ציון -->                        
            <col style="width: 30px;" /> <!-- Actions column -->
          </colgroup>
          <thead>
            <tr>
              <th class="title">תאריך</th>
              <th class="title">קפה</th>
              <th class="title">בית קליה</th>
              <th class="title">מוצא</th>
              <th class="title">תאריך קליה</th>
              <th class="title">תאריך רכישה</th>
              <th class="title">שיטת חליטה</th>
              <th class="title">בורר טחינה</th>
              <th class="title">טמפ' (°C)</th>
              <th class="title">פרה אינפיוזן</th>
              <th class="title">כמות קפה</th>
              <th class="title">זמן  חליטה</th>
              <th class="title">תפוקת קפה</th>
              <th class="title">גוף</th>
              <th class="title">ארומה</th>
              <th class="title">טעמים</th>
              <th class="title">ציון</th>
              <th class="title">מחיקה</th>
            </tr>
          </thead>
          <tbody>
            {% for brew in brewlogs %}
            <tr>
              <td>{{ brew.brew_date }}</td>
              <td>{{ brew.bean_name }}</td>
              <td>{{ brew.roaster_name }}</td>
              <td>{{ brew.origin }}</td>
              <td class="centered">{{ brew.bean_roasting_date }}</td>
              <td class="centered">{{ brew.bean_purchase_date }}</td>
              <td class="centered">{{ brew.brew_method }}</td>
              <td class="centered">{{ brew.grind_settings }}</td>
              <td class="centered">{{ brew.brew_temp }}</td>
              <td class="centered">{{ brew.PreInfusionTimeSec }}</td>
              <td class="centered">{{ brew.coffee_dose }}</td>
              <td class="centered">{{ brew.brew_time }}</td>
              <td class="centered">{{ brew.coffee_output }}</td>
              <td class="centered">{{ brew.body }}</td>
              <td>{{ brew.aroma }}</td>        
              <td>
                {% if brew.sweetness or brew.acidity or brew.bitterness %}
                {% if brew.sweetness %}<b>מתיקות</b>: {{ brew.sweetness }}<br>{% endif %}
                  {% if brew.acidity %}<b>חמיצות</b>: {{ brew.acidity }}<br>{% endif %}
                  {% if brew.bitterness %}<b>מרירות</b>: {{ brew.bitterness }}{% endif %}
                {% endif %}
              </td>
              <td class="centered rating-{{ brew.overall_rating }}">{{ brew.overall_rating }}</td>
              <td class="centered actions-col">
                <form method="post" action="{{ url_for('delete_brew', brewlog_id=brew.brewlog_id) }}" style="display: inline-block;">
                  <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                  <button type="submit" class="delete-btn" onclick="return confirm('האם אתה בטוח שברצונך למחוק פול קפה זה?');">מחק</button>
                </form>
              </td>
              <!-- <td class="centered actions-col">
                <button class="delete-btn" onclick="confirmDelete('{{ brew.id }}')">מחק</button>
              </td> -->
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  {% else %}
    <div class="no-records">
      <p>לא נמצאו רשומות חליטה.</p>
      <p>נסה להוסיף חליטות חדשות מדף <a href="{{ url_for('search_beans') }}">חיפוש פולי קפה</a>.</p>
    </div>
  {% endif %}
  </div>
{% endblock %}
