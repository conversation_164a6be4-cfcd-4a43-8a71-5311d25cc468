{% extends "base.html" %}
{% block title %}עדכון פולי קפה{% endblock %}
{% block content %}
<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>Update Beans</title>
     <style>
          body {font-family: sans-serif; margin: 20px; direction: rtl; text-align: right;}
          form label { display: block; margin-bottom: 5px; }
          form select { margin-bottom: 10px; padding: 5px; direction: rtl;}
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          table, th, td { border: 1px solid black; }
          th, td { padding: 10px; text-align: right; }
        </style>
</head>
<body>
    <h1>עדכון פולי קפה</h1>
    <form method="post">
        {{ csrf_token() }} {# Add CSRF token field #}
        <label for="roaster_id">בחר קולה:</label>
        <select name="roaster_id" id="roaster_id" style="direction: rtl;">
            <option value="">ב<PERSON>ר קולה</option>
            {% for roaster in roasters %}
                <option value="{{ roaster.roaster_id }}">{{ roaster.name }}</option>
            {% endfor %}
        </select><br>
        <input type="checkbox" id="list_all" name="list_all" value="true">
        <label for="list_all">הצג את כל פולי הקפה</label><br>
        <input type="submit" value="הצג פולי קפה">
    </form>
    <a href="{{ url_for('home') }}">חזור לדף הבית</a>

    {% if beans %}
    <h2>רשימת פולי קפה</h2>
    <table>
        <thead>
            <tr>
                <th>שם פולי קפה</th>
                <th>מקור</th>
                <th>עיבוד</th>
                <th>גובה</th>
                <th>טעמים</th>
                <th>חומציות</th>
                <th>אחרי טעם</th>
                <th>דרגת קלייה</th>
                <th>ערביקה</th>
                <th>רובוסטה</th>
                <th>מחיר</th>
                <th>משקל</th>
                <th>תמונה</th>
                <th>פעולות</th>
            </tr>
        </thead>
        <tbody>
            {% for bean in beans %}
            <tr>
                <td>{{ bean.bean_name }}</td>
                <td>{{ bean.origin }}</td>
                <td>{{ bean.processing }}</td>
                <td>{{ bean.elevation }}</td>
                <td>{{ bean.flavors }}</td>
                <td>{{ bean.acidity }}</td>
                <td>{{ bean.after_taste }}</td>
                <td>{{ bean.roast_level }}</td>
                <td>{{ bean.arabica }}</td>
                <td>{{ bean.robusta }}</td>
                <td>{{ bean.price }}</td>
                <td>{{ bean.weight }}</td>
                <td><img src="{{ bean.image_file }}" alt="Image" style="width: 50px; height: 50px;"></td>
                <td>
                    <a href="{{ url_for('edit_bean', bean_id=bean.bean_id) }}">ערוך</a>
                    <form action="{{ url_for('delete_bean', bean_id=bean.bean_id) }}" method="post" style="display:inline;">
                        {{ csrf_token() }} {# Add CSRF token field #}
                        <button type="submit">מחק</button>
                    </form>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% endif %}
</body>
</html>
{% endblock %}