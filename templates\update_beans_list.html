<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
 <meta charset="UTF-8">
  <title>Bean List</title>
    <style>
         body {font-family: sans-serif; margin: 20px; direction: rtl; text-align: right;}
          table {width: 100%; border-collapse: collapse; margin-bottom: 20px;}
           th, td {border: 1px solid #ddd; padding: 8px; text-align: right;}
             th {background-color: #f2f2f2;}
    </style>
</head>
<body>
 <h1>עדכון פולי קפה</h1>
   <h2>רשימת פולי קפה עבור {{ roasters[0].name if roasters[0].roaster_id == selected_roaster else ""  }}</h2>
   <table>
       <thead>
           <tr>
               <th>שם פול</th>
               <th>מקור</th>
               <th>עיבוד</th>
               <th>דרגת קלייה</th>
               <th>פעולות</th>
           </tr>
       </thead>
       <tbody>
            {% for bean in beans %}
           <tr>
              <td>{{ bean.bean_name }}</td>
               <td>{{ bean.origin }}</td>
              <td>{{ bean.processing }}</td>
             <td>{{ bean.roast_level }}</td>
               <td>
                  <a href="{{ url_for('edit_bean', bean_id=bean.bean_id) }}">עריכה</a>
               <form method="post" action="{{ url_for('delete_bean', bean_id=bean.bean_id) }}" style="display: inline-block;">
                   <input type="submit" value="מחק" onclick="return confirm('האם אתה בטוח שברצונך למחוק פול קפה זה?');">
               </form>
              </td>
           </tr>
            {% endfor %}
       </tbody>
   </table>
  <a href="{{ url_for('update_beans') }}">חזור לבחירת קולה</a>
</body>
</html>