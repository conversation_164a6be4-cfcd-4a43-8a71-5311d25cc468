{% extends 'base.html' %}

{% block title %}Verify Your Email{% endblock %}

{% block content %}
<div class="container mx-auto mt-10 p-5 text-center">
    <h1 class="text-2xl font-bold mb-4">Please Verify Your Email</h1>

    {# Display flashed messages if any #}
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        <div class="mb-4">
          {% for category, message in messages %}
            <div class="p-2 rounded {{ 'bg-blue-100 text-blue-700' if category == 'info' else 'bg-yellow-100 text-yellow-700' if category == 'warning' else 'bg-red-100 text-red-700' if category == 'danger' else 'bg-green-100 text-green-700' }}" role="alert">
              {{ message }}
            </div>
          {% endfor %}
        </div>
      {% endif %}
    {% endwith %}

    <p class="mb-4">A verification email has been sent to <strong>{{ g.user.email if g.user else 'your email address' }}</strong>.</p>
    <p class="mb-4">Please check your inbox (and spam folder) and click the verification link to activate your account.</p>
    <p class="mb-4">Once verified, you may need to log out and log back in.</p>
    <p class="mb-4">If you haven't received the email after a few minutes, you can try resending it.</p>

    <button id="resendVerificationBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
        Resend Verification Email
    </button>

    <p id="resendMessage" class="mt-4 text-green-600"></p>
    <p id="resendError" class="mt-4 text-red-600"></p>

    <p class="mt-6"><a href="{{ url_for('logout') }}" class="text-blue-500 hover:underline">Logout</a></p>
</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const resendBtn = document.getElementById('resendVerificationBtn');
        const resendMsg = document.getElementById('resendMessage');
        const resendErr = document.getElementById('resendError');

        // Check if user is logged in (we still use Firebase for authentication state)
        if (typeof firebase !== 'undefined' && firebase.auth) {
            const auth = firebase.auth();

            // Check immediately if user is verified on page load
            auth.onAuthStateChanged((user) => {
                if (user && user.emailVerified) {
                    // If verified, redirect to home with a success message
                    window.location.href = "{{ url_for('home') }}?verified=true";
                } else if (user) {
                    // User exists but not verified, enable button
                    resendBtn.disabled = false;
                } else {
                    // No user logged in, disable button and show error
                    resendErr.textContent = 'You must be logged in to resend verification email.';
                    resendBtn.disabled = true;
                }
            });

            resendBtn.addEventListener('click', () => {
                resendMsg.textContent = '';
                resendErr.textContent = '';
                resendBtn.disabled = true; // Prevent multiple clicks
                resendBtn.textContent = 'Sending...';

                // Use backend endpoint instead of Firebase
                fetch('/send-verification-email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token() }}'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resendMsg.textContent = 'Verification email sent successfully! Please check your inbox.';
                        resendBtn.textContent = 'Resend Verification Email';
                        // Keep button disabled for a short period to prevent spamming
                        setTimeout(() => { resendBtn.disabled = false; }, 5000);
                    } else {
                        resendErr.textContent = data.message || 'Error sending email. Please try again later.';
                        resendBtn.textContent = 'Resend Verification Email';
                        resendBtn.disabled = false;
                    }
                })
                .catch((error) => {
                    console.error('Error resending verification email:', error);
                    resendErr.textContent = 'Error sending email. Please try again later.';
                    resendBtn.textContent = 'Resend Verification Email';
                    resendBtn.disabled = false;
                });
            });
        } else {
             resendErr.textContent = 'Authentication system not available. Cannot resend email.';
             resendBtn.disabled = true;
             resendBtn.style.display = 'none'; // Hide button if auth isn't ready
        }
    });
</script>
{% endblock %}