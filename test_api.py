#!/usr/bin/env python3
"""
Simple test script to test the API endpoint locally
"""
import requests
import json

def test_api():
    # Test data
    test_data = {
        "origin": [],
        "flavors": [],
        "processing": "",
        "roast_level": "",
        "elevation": [],
        "arabica": 0,
        "robusta": 0,
        "singleorigin": 0,
        "mix": 0,
        "speciality": 0,
        "decaf": 0,
        "brew_methods": [],
        "roasters": [],
        "isra_content": 0,
        "sort_by": None,
        "sort_dir": "asc"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer demo-key-12345-67890-abcdef"
    }
    
    try:
        # Make the request
        response = requests.post(
            "http://127.0.0.1:5000/api/search_beans",
            headers=headers,
            json=test_data,
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("API Response Structure:")
            print(f"- Data type: {type(data)}")
            if isinstance(data, dict):
                print(f"- Keys: {list(data.keys())}")
                if 'data' in data:
                    print(f"- Number of beans: {len(data['data'])}")
                    if data['data']:
                        print(f"- Sample bean keys: {list(data['data'][0].keys())}")
            else:
                print(f"- Number of items: {len(data)}")
                if data:
                    print(f"- Sample item keys: {list(data[0].keys())}")
            print("✅ API test successful!")
        else:
            print(f"❌ API test failed!")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - make sure Flask app is running on http://127.0.0.1:5000")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_api()
