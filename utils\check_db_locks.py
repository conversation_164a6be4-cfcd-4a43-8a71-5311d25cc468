import sqlite3
import os
import time
import sys
from pathlib import Path

# Get the project root directory
project_root = str(Path(__file__).parent.parent)
sys.path.insert(0, project_root)

DATABASE = os.path.join(project_root, 'coffee_database.db')

def check_database_locks():
    """Check if the database has any locks and try to resolve them"""
    print(f"Checking database locks on: {DATABASE}")
    
    try:
        # Try a quick connection
        conn = sqlite3.connect(DATABASE, timeout=1)
        cursor = conn.cursor()
        
        # Check if we can get an immediate lock
        try:
            cursor.execute("BEGIN IMMEDIATE")
            print("No locks detected - database is available")
            cursor.execute("COMMIT")
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e):
                print("Database is locked!")
                
                # Try to identify what's locking it
                print("Attempting to identify lock holder...")
                
                # On Unix systems, we could use fuser, lsof, etc.
                # On Windows, we're more limited, but we can try a few things
                
                # Try to see if the lock is temporary by waiting
                print("Waiting for 5 seconds to see if lock clears...")
                time.sleep(5)
                
                try:
                    # Try again after waiting
                    cursor.execute("BEGIN IMMEDIATE")
                    print("Lock was cleared after waiting")
                    cursor.execute("COMMIT")
                except sqlite3.OperationalError as e2:
                    print(f"Lock persists: {e2}")
                    print("\nPossible solutions:")
                    print("1. Restart the application")
                    print("2. Make a copy of the database file")
                    print("3. Use the vacuum command to rebuild the database")
            else:
                print(f"Other database error: {e}")
        
        # Check if we can write to the database
        try:
            # Create a temporary table just to test writing
            cursor.execute("CREATE TEMPORARY TABLE lock_test (id INTEGER)")
            cursor.execute("INSERT INTO lock_test VALUES (1)")
            cursor.execute("DROP TABLE lock_test")
            print("Write test passed - database is writeable")
        except sqlite3.OperationalError as e:
            print(f"Write test failed: {e}")
        
        conn.close()
    
    except Exception as e:
        print(f"Error connecting to database: {e}")

def attempt_vacuum():
    """Try to vacuum the database which can sometimes clear locks"""
    try:
        print("Attempting VACUUM command...")
        conn = sqlite3.connect(DATABASE, timeout=10)
        conn.execute("VACUUM")
        conn.close()
        print("VACUUM completed successfully")
        return True
    except Exception as e:
        print(f"VACUUM failed: {e}")
        return False

if __name__ == "__main__":
    print("=== Database Lock Checker ===")
    check_database_locks()
    
    response = input("\nAttempt VACUUM to clear locks? (y/n): ")
    if response.lower() == 'y':
        attempt_vacuum()
