"""
<PERSON><PERSON><PERSON> to check the elevation values in the beans table
"""
import sqlite3
import os
import sys

# Add the parent directory to sys.path to allow importing app's module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the database path
from app import DATABAS<PERSON>

def check_elevations():
    """Check the elevation column in beans table"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # Check table structure
    print("=== BEANS TABLE STRUCTURE ===")
    cursor.execute("PRAGMA table_info(beans)")
    columns = cursor.fetchall()
    for col in columns:
        print(f"{col['name']} ({col['type']})")
    
    # Check elevation values
    print("\n=== ELEVATION VALUES ===")
    cursor.execute("SELECT bean_id, bean_name, elevation FROM beans LIMIT 10")
    rows = cursor.fetchall()
    for row in rows:
        print(f"Bean #{row['bean_id']} ({row['bean_name']}): elevation = {row['elevation']} (type: {type(row['elevation'])})")
    
    # Check elevation distribution
    print("\n=== ELEVATION RANGES ===")
    cursor.execute("SELECT COUNT(*) FROM beans WHERE elevation > 0 AND elevation < 1000")
    count = cursor.fetchone()[0]
    print(f"0-1000m: {count} beans")
    
    cursor.execute("SELECT COUNT(*) FROM beans WHERE elevation > 1000 AND elevation < 1501")
    count = cursor.fetchone()[0]
    print(f"1001-1500m: {count} beans")
    
    cursor.execute("SELECT COUNT(*) FROM beans WHERE elevation > 1500 AND elevation < 2001")
    count = cursor.fetchone()[0]
    print(f"1501-2000m: {count} beans")
    
    cursor.execute("SELECT COUNT(*) FROM beans WHERE elevation > 2000")
    count = cursor.fetchone()[0]
    print(f">2000m: {count} beans")
    
    cursor.execute("SELECT COUNT(*) FROM beans WHERE elevation IS NULL OR elevation = ''")
    count = cursor.fetchone()[0]
    print(f"No elevation data: {count} beans")
    
    conn.close()

if __name__ == "__main__":
    check_elevations()
