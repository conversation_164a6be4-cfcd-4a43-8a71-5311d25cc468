import sqlite3
import os
import time
import sys
import logging
import logging
from pathlib import Path

# Get the project root directory
project_root = str(Path(__file__).parent.parent)
sys.path.insert(0, project_root)

DATABASE = os.path.join(project_root, 'coffee_database.db')

def get_db_connection(timeout=20):
    """Get a database connection with retry logic on locked database"""
    attempts = 0
    max_attempts = 5
    last_error = None
    
    while attempts < max_attempts:
        try:
            conn = sqlite3.connect(DATABASE, timeout=timeout)
            conn.row_factory = sqlite3.Row
            return conn
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e):
                attempts += 1
                last_error = e
                wait_time = attempts * 2  # Exponential backoff
                print(f"Database locked, waiting {wait_time} seconds (attempt {attempts}/{max_attempts})...")
                time.sleep(wait_time)
            else:
                raise
    
    raise last_error

def delete_brewlog_record(record_id):
    """Safely delete a record from the brewlog table"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN IMMEDIATE")

        # Check if the record exists
        cursor.execute("SELECT COUNT(*) FROM brewlog WHERE brewlog_id = ?", (record_id,))
        if cursor.fetchone()[0] == 0:
            conn.rollback()
            conn.close()
            return False, "Record not found"

        # Delete the record
        cursor.execute("DELETE FROM brewlog WHERE brewlog_id = ?", (record_id,))

        # Commit the transaction
        conn.commit()
        conn.close()
        return True, "Record deleted successfully"
    except sqlite3.OperationalError as e:
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        # Log the actual error but return a generic message
        logging.error(f"Database operational error deleting record {record_id}: {e}")
        return False, "Database operation failed"
    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        # Log the actual error but return a generic message
        logging.error(f"Error deleting record {record_id}: {e}", exc_info=True)
        return False, "An error occurred while processing your request"

def delete_brewlog_for_user(uid, record_id=None):
    """Delete brewlog records for a specific user, optionally filtered by record ID"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN IMMEDIATE")

        if record_id:
            # Delete specific record if it belongs to the user
            cursor.execute("DELETE FROM brewlog WHERE brewlog_id = ? AND uid = ?", (record_id, str(uid)))
        else:
            # Delete all records for the user
            cursor.execute("DELETE FROM brewlog WHERE uid = ?", (str(uid),))

        affected_rows = cursor.rowcount

        # Commit the transaction
        conn.commit()
        conn.close()

        return True, f"Deleted {affected_rows} record(s)"
    except sqlite3.Error as e:
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        # Log the actual error but return a generic message
        logging.error(f"Database error deleting records for user {uid}: {e}")
        return False, "Database operation failed"
    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        # Log the actual error but return a generic message
        logging.error(f"Error deleting records for user {uid}: {e}", exc_info=True)
        return False, "An error occurred while processing your request"

if __name__ == "__main__":
    # Simple test to check if we can connect to the database
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM brewlog")
        count = cursor.fetchone()[0]
        print(f"Found {count} records in brewlog table")
        conn.close()
        print("Database connection test successful")
    except Exception as e:
        print(f"Database connection test failed: {e}")
