"""
Debugging utilities for the Coffee Project application.
"""

def log_sql_debug(query, params=None):
    """
    Format and print SQL query and parameters for debugging.
    
    Args:
        query (str): The SQL query string
        params (list, optional): Query parameters
    """
    print("\n=== SQL DEBUG ===")
    print("QUERY:", query)
    if params:
        print("PARAMS:", params)
    print("=================\n")

def log_request_data(data, route_name=None):
    """
    Log request data received in a route.
    
    Args:
        data (dict): The request data
        route_name (str, optional): Name of the route for context
    """
    print("\n=== REQUEST DATA ===")
    if route_name:
        print(f"ROUTE: {route_name}")
    
    for key, value in data.items():
        print(f"{key}: {value} (type: {type(value)})")
    print("====================\n")
