#!/usr/bin/env python3
"""
Email action utilities for handling email verification and password reset tokens.
This module provides secure token generation and verification for email-based actions.
"""

import os
import time
from itsdangerous import URLSafeTimedSerializer, SignatureExpired, BadSignature


class EmailActionError(Exception):
    """Custom exception for email action errors"""
    pass


def get_email_serializer(secret_key=None, salt="email-actions"):
    """
    Get a URLSafeTimedSerializer for email actions.

    Args:
        secret_key (str, optional): Secret key for signing. Uses FLASK_SECRET_KEY if not provided.
        salt (str): Salt for the serializer

    Returns:
        URLSafeTimedSerializer: Configured serializer
    """
    if not secret_key:
        secret_key = os.environ.get('FLASK_SECRET_KEY')
        if not secret_key:
            raise EmailActionError("No secret key available for email token generation")

    return URLSafeTimedSerializer(secret_key, salt=salt)


def generate_email_verification_token(email, uid):
    """
    Generate a secure token for email verification.

    Args:
        email (str): User's email address
        uid (str): User's Firebase UID

    Returns:
        str: Secure token for email verification
    """
    serializer = get_email_serializer(salt="email-verification")

    # Create payload with email, uid, and timestamp
    payload = {
        'email': email,
        'uid': uid,
        'action': 'verify_email',
        'timestamp': int(time.time())
    }

    return serializer.dumps(payload)


def generate_password_reset_token(email, uid):
    """
    Generate a secure token for password reset.

    Args:
        email (str): User's email address
        uid (str): User's Firebase UID

    Returns:
        str: Secure token for password reset
    """
    serializer = get_email_serializer(salt="password-reset")

    # Create payload with email, uid, and timestamp
    payload = {
        'email': email,
        'uid': uid,
        'action': 'reset_password',
        'timestamp': int(time.time())
    }

    return serializer.dumps(payload)


def verify_email_token(token, max_age=3600):
    """
    Verify an email verification token.

    Args:
        token (str): Token to verify
        max_age (int): Maximum age in seconds (default: 1 hour)

    Returns:
        dict: Payload if valid, None if invalid

    Raises:
        EmailActionError: If token is invalid or expired
    """
    serializer = get_email_serializer(salt="email-verification")

    try:
        payload = serializer.loads(token, max_age=max_age)

        # Validate payload structure
        required_fields = ['email', 'uid', 'action']
        if not all(field in payload for field in required_fields):
            raise EmailActionError("Invalid token payload")

        if payload.get('action') != 'verify_email':
            raise EmailActionError("Token not for email verification")

        return payload

    except SignatureExpired:
        raise EmailActionError("Verification link has expired")
    except BadSignature:
        raise EmailActionError("Invalid verification link")


def verify_password_reset_token(token, max_age=3600):
    """
    Verify a password reset token.

    Args:
        token (str): Token to verify
        max_age (int): Maximum age in seconds (default: 1 hour)

    Returns:
        dict: Payload if valid, None if invalid

    Raises:
        EmailActionError: If token is invalid or expired
    """
    serializer = get_email_serializer(salt="password-reset")

    try:
        payload = serializer.loads(token, max_age=max_age)

        # Validate payload structure
        required_fields = ['email', 'uid', 'action']
        if not all(field in payload for field in required_fields):
            raise EmailActionError("Invalid token payload")

        if payload.get('action') != 'reset_password':
            raise EmailActionError("Token not for password reset")

        return payload

    except SignatureExpired:
        raise EmailActionError("Password reset link has expired")
    except BadSignature:
        raise EmailActionError("Invalid password reset link")


def create_verification_email_html(verification_url, user_email):
    """
    Create HTML content for email verification email in Hebrew with RTL support.

    Args:
        verification_url (str): URL for email verification
        user_email (str): User's email address

    Returns:
        str: HTML content for the email
    """
    return f"""
    <html dir="rtl" lang="he">
      <head>
        <meta charset="UTF-8">
        <style>
          body {{
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
            text-align: right;
          }}
          .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
          .header {{
            background-color: #8B4513;
            color: white;
            padding: 20px;
            text-align: center;
          }}
          .content {{
            padding: 20px;
            background-color: #f9f9f9;
            text-align: right;
            direction: rtl;
          }}
          .button {{
            display: inline-block;
            padding: 12px 24px;
            background-color: #8B4513;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
          }}
          .footer {{
            padding: 20px;
            text-align: center;
            font-size: 12px;
            color: #666;
          }}
          .email-address {{
            direction: ltr;
            display: inline-block;
          }}
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Israeli Coffee - אימות כתובת אימייל</h1>
          </div>
          <div class="content">
            <h2>ברוכים הבאים ל-Israeli Coffee!</h2>
            <p>תודה שנרשמת ל-Israeli Coffee עם כתובת האימייל <strong class="email-address">{user_email}</strong>. כדי להשלים את ההרשמה ולהתחיל לחקור את בתי הקלייה הטובים ביותר בישראל, אנא אמת את כתובת האימייל שלך.</p>

            <p>לחץ על הכפתור למטה כדי לאמת את כתובת האימייל שלך:</p>

            <div style="text-align: center;">
              <a href="{verification_url}" class="button">אמת כתובת אימייל</a>
            </div>

            <p>אם הכפתור לא עובד, תוכל להעתיק ולהדביק את הקישור הזה בדפדפן שלך:</p>
            <p style="direction: ltr; text-align: left;"><a href="{verification_url}">{verification_url}</a></p>

            <p>קישור האימות הזה יפוג תוך שעה אחת מסיבות אבטחה.</p>

            <p>אם לא יצרת חשבון ב-Israeli Coffee, תוכל להתעלם מהאימייל הזה בבטחה.</p>
          </div>
          <div class="footer">
            <p>Israeli Coffee - מחברים אותך עם בתי הקלייה הטובים ביותר בישראל</p>
            <p class="email-address">Email: <EMAIL></p>
          </div>
        </div>
      </body>
    </html>
    """


def create_password_reset_email_html(reset_url, user_email):
    """
    Create HTML content for password reset email in Hebrew with RTL support.

    Args:
        reset_url (str): URL for password reset
        user_email (str): User's email address

    Returns:
        str: HTML content for the email
    """
    return f"""
    <html dir="rtl" lang="he">
      <head>
        <meta charset="UTF-8">
        <style>
          body {{
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
            text-align: right;
          }}
          .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
          .header {{
            background-color: #8B4513;
            color: white;
            padding: 20px;
            text-align: center;
          }}
          .content {{
            padding: 20px;
            background-color: #f9f9f9;
            text-align: right;
            direction: rtl;
          }}
          .button {{
            display: inline-block;
            padding: 12px 24px;
            background-color: #8B4513;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
          }}
          .footer {{
            padding: 20px;
            text-align: center;
            font-size: 12px;
            color: #666;
          }}
          .warning {{
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            text-align: right;
            direction: rtl;
          }}
          .email-address {{
            direction: ltr;
            display: inline-block;
          }}
          ul {{
            text-align: right;
            direction: rtl;
            padding-right: 20px;
          }}
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Israeli Coffee - איפוס סיסמה</h1>
          </div>
          <div class="content">
            <h2>בקשה לאיפוס סיסמה</h2>
            <p>קיבלנו בקשה לאיפוס הסיסמה עבור חשבון Israeli Coffee שלך (<strong class="email-address">{user_email}</strong>).</p>

            <p>לחץ על הכפתור למטה כדי לאפס את הסיסמה שלך:</p>

            <div style="text-align: center;">
              <a href="{reset_url}" class="button">אפס סיסמה</a>
            </div>

            <p>אם הכפתור לא עובד, תוכל להעתיק ולהדביק את הקישור הזה בדפדפן שלך:</p>
            <p style="direction: ltr; text-align: left;"><a href="{reset_url}">{reset_url}</a></p>

            <div class="warning">
              <strong>הודעת אבטחה:</strong>
              <ul>
                <li>קישור איפוס הסיסמה הזה יפוג תוך שעה אחת</li>
                <li>אם לא ביקשת איפוס סיסמה זה, תוכל להתעלם מהאימייל הזה בבטחה</li>
                <li>הסיסמה שלך לא תשונה אלא אם תלחץ על הקישור למעלה ותשלים את התהליך</li>
              </ul>
            </div>
          </div>
          <div class="footer">
            <p>Israeli Coffee - מחברים אותך עם בתי הקלייה הטובים ביותר בישראל</p>
            <p class="email-address">Email: <EMAIL></p>
          </div>
        </div>
      </body>
    </html>
    """
