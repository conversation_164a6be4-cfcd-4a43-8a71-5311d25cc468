from itsdangerous import URLSafeSerializer

def get_serializer(secret_key, salt="roaster-salt"):
    # Create and return a URLSafeSerializer instance using the provided secret key and salt.
    return URLSafeSerializer(secret_key, salt=salt)

def encrypt_id(secret_key, value):
    serializer = get_serializer(secret_key)
    return serializer.dumps(value)

def decrypt_id(secret_key, token):
    serializer = get_serializer(secret_key)
    return serializer.loads(token)
