"""
Error handling utilities for the Coffee Project application.
Provides centralized error handling functions to ensure consistent error responses.
"""

import logging
import traceback
from flask import jsonify
import sqlite3
import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('coffee_project')

def handle_database_error(e, operation=None, context=None):
    """
    Handle database-related errors with proper logging and user-friendly messages.
    
    Args:
        e (Exception): The exception that was raised
        operation (str, optional): Description of the database operation being performed
        context (dict, optional): Additional context information
        
    Returns:
        tuple: (JSON response, HTTP status code)
    """
    logger = logging.getLogger()
    # Fallback for error_id/time if no handlers are present
    if logger.handlers:
        error_id = logger.handlers[0].formatter.formatTime(
            logging.LogRecord('', 0, '', 0, '', (), None)
        )
    else:
        error_id = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Log the full error with traceback
    logger.error(f"Database error during {operation or 'unknown operation'}: {str(e)}")
    logger.error(traceback.format_exc())
    
    if context:
        logger.info(f"Error context: {context}")
    
    # Determine the specific error type and provide appropriate message
    if isinstance(e, sqlite3.OperationalError):
        if "database is locked" in str(e):
            return jsonify({
                'success': False,
                'error': 'Database is temporarily unavailable. Please try again in a moment.',
                'error_id': error_id
            }), 503  # Service Unavailable
        elif "no such table" in str(e):
            return jsonify({
                'success': False,
                'error': 'Database schema error. Please contact support.',
                'error_id': error_id
            }), 500
        else:
            return jsonify({
                'success': False,
                'error': 'A database operational error occurred.',
                'error_id': error_id
            }), 500
    elif isinstance(e, sqlite3.IntegrityError):
        if "UNIQUE constraint failed" in str(e):
            return jsonify({
                'success': False,
                'error': 'This record already exists.',
                'error_id': error_id
            }), 409  # Conflict
        elif "FOREIGN KEY constraint failed" in str(e):
            return jsonify({
                'success': False,
                'error': 'Referenced record does not exist.',
                'error_id': error_id
            }), 400  # Bad Request
        else:
            return jsonify({
                'success': False,
                'error': 'Data integrity error.',
                'error_id': error_id
            }), 400
    else:
        # Generic database error
        return jsonify({
            'success': False,
            'error': 'A database error occurred.',
            'error_id': error_id
        }), 500

def handle_validation_error(missing_fields=None, invalid_fields=None):
    """
    Handle validation errors with specific feedback
    
    Args:
        missing_fields: List of required fields that are missing
        invalid_fields: List of fields with invalid values
        
    Returns:
        tuple: (JSON response, HTTP status code)
    """
    errors = []
    
    if missing_fields:
        errors.append(f"Missing required fields: {', '.join(missing_fields)}")
    
    if invalid_fields:
        errors.append(f"Invalid values for: {', '.join(invalid_fields)}")
    
    if not errors:
        errors.append("Invalid input data")

    return jsonify({
        'success': False,
        'error': 'Validation error',
        'details': errors
    }), 400

def handle_auth_error(e, operation=None):
    """
    Handle authentication and authorization errors
    
    Args:
        e (Exception): The exception that was raised
        operation (str, optional): Description of the operation being performed
        
    Returns:
        tuple: (JSON response, HTTP status code)
    """
    logger.warning(f"Auth error during {operation or 'unknown operation'}: {str(e)}")
    
    # Determine the specific error type
    error_message = str(e).lower()
    
    if "token expired" in error_message or "expired token" in error_message:
        return jsonify({
            'success': False,
            'error': 'Your session has expired. Please log in again.'
        }), 401
    elif "invalid token" in error_message:
        return jsonify({
            'success': False,
            'error': 'Invalid authentication token. Please log in again.'
        }), 401
    elif "permission" in error_message or "not authorized" in error_message:
        return jsonify({
            'success': False,
            'error': 'You do not have permission to perform this action.'
        }), 403
    else:
        return jsonify({
            'success': False,
            'error': 'Authentication error. Please log in again.'
        }), 401

def handle_general_error(e, operation=None):
    """
    Handle general errors with proper logging
    
    Args:
        e (Exception): The exception that was raised
        operation (str, optional): Description of the operation being performed
        
    Returns:
        tuple: (JSON response, HTTP status code)
    """
    error_id = logger.handlers[0].formatter.formatTime(logging.LogRecord('', 0, '', 0, '', (), None))
    
    # Log the full error with traceback
    logger.error(f"Error during {operation or 'unknown operation'}: {str(e)}")
    logger.error(traceback.format_exc())
    
    # Return a generic error message to the user
    return jsonify({
        'success': False,
        'error': 'An unexpected error occurred. Please try again later.',
        'error_id': error_id
    }), 500
