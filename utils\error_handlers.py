"""
Error handling utilities for the Coffee application.
Provides standardized error responses and logging without exposing internal details.
"""
import logging
from flask import jsonify

# Configure logging
logger = logging.getLogger(__name__)

# Error categories for client-facing messages
ERROR_CATEGORIES = {
    # Authentication errors
    'auth': {
        'invalid_token': 'Authentication failed. Please log in again.',
        'expired_token': 'Your session has expired. Please log in again.',
        'unauthorized': 'You are not authorized to perform this action.',
        'missing_token': 'Authentication required. Please log in.',
    },
    # Database errors
    'database': {
        'connection': 'Database connection error. Please try again later.',
        'query': 'Error processing your request. Please try again later.',
        'not_found': 'The requested resource was not found.',
        'constraint': 'The operation could not be completed due to data constraints.',
    },
    # Input validation errors
    'validation': {
        'missing_field': 'Required information is missing.',
        'invalid_format': 'The provided information is not in the correct format.',
        'invalid_value': 'One or more values are invalid.',
    },
    # General errors
    'general': {
        'server_error': 'An unexpected error occurred. Please try again later.',
        'not_implemented': 'This feature is not yet available.',
        'service_unavailable': 'Service temporarily unavailable. Please try again later.',
    }
}

def log_error(error, error_type="Exception", additional_info=None):
    """
    Log an error with appropriate severity and context
    
    Args:
        error: The exception object
        error_type: Type of error for categorization
        additional_info: Any additional context that might help debugging
    """
    error_message = f"{error_type}: {str(error)}"
    if additional_info:
        error_message += f" | Context: {additional_info}"
    
    logger.error(error_message, exc_info=True)

def handle_error(error, error_category='general', error_type='server_error', status_code=500, log=True):
    """
    Create a standardized error response without exposing internal details
    
    Args:
        error: The exception object or error message
        error_category: Category from ERROR_CATEGORIES
        error_type: Specific error type within the category
        status_code: HTTP status code to return
        log: Whether to log the error
        
    Returns:
        A tuple of (jsonify response, status code)
    """
    # Log the actual error with full details for debugging
    if log:
        log_error(error, error_type)
    
    # Get user-facing message
    if error_category in ERROR_CATEGORIES and error_type in ERROR_CATEGORIES[error_category]:
        user_message = ERROR_CATEGORIES[error_category][error_type]
    else:
        user_message = ERROR_CATEGORIES['general']['server_error']
    
    # Return sanitized response
    return jsonify({
        'success': False,
        'error': user_message
    }), status_code

def handle_validation_error(missing_fields=None, invalid_fields=None):
    """
    Handle validation errors with specific feedback
    
    Args:
        missing_fields: List of required fields that are missing
        invalid_fields: List of fields with invalid values
        
    Returns:
        A tuple of (jsonify response, status code)
    """
    errors = []
    
    if missing_fields:
        errors.append(f"Missing required fields: {', '.join(missing_fields)}")
    
    if invalid_fields:
        errors.append(f"Invalid values for: {', '.join(invalid_fields)}")
    
    if not errors:
        errors.append("Invalid input data")

    return jsonify({
        'success': False,
        'error': 'Validation error',
        'details': errors
    }), 400