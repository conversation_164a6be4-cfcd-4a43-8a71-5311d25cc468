import logging
import json
import os
from datetime import datetime

class JsonFormatter(logging.Formatter):
    def format(self, record):
        log_record = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "message": record.getMessage()
        }
        # Add other record attributes if needed, e.g., record.name, record.pathname
        if hasattr(record, 'custom_data'):
            log_record.update(record.custom_data)
        return json.dumps(log_record)

def setup_activity_logger(log_file_path='logs/activity.log', logger_name='activity_logger'):
    """
    Sets up a logger to write activity logs in JSON format to the specified file.
    """
    # Ensure the directory for the log file exists
    log_dir = os.path.dirname(log_file_path)
    if log_dir and not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir)
        except OSError as e:
            # Handle potential race condition if directory is created by another process
            if not os.path.isdir(log_dir):
                print(f"Error creating log directory {log_dir}: {e}")
                # Fallback or raise, depending on desired behavior
                # For now, we'll print and let it potentially fail on handler creation

    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.INFO)

    # Prevent duplicate handlers if logger is already configured
    if not logger.handlers:
        # File Handler
        try:
            file_handler = logging.FileHandler(log_file_path)
            file_handler.setFormatter(JsonFormatter())
            logger.addHandler(file_handler)
        except Exception as e:
            print(f"Error setting up file handler for {log_file_path}: {e}")
            # Optionally, add a NullHandler or re-raise to indicate critical failure
            # For now, we'll print the error and the logger might not work as expected.

        # Optional: Console Handler for debugging (can be removed for production)
        # console_handler = logging.StreamHandler()
        # console_handler.setFormatter(JsonFormatter())
        # logger.addHandler(console_handler)

    return logger

if __name__ == '__main__':
    # Example usage:
    # Get the ACTIVITY_LOG_PATH from environment or use default
    default_log_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs', 'activity_test.log')
    log_path = os.environ.get('ACTIVITY_LOG_PATH', default_log_path)
    
    activity_logger = setup_activity_logger(log_file_path=log_path)
    
    # Test logging
    activity_logger.info("User logged in", extra={'custom_data': {"user_id": "test_user_123", "action": "login"}})
    activity_logger.warning("Failed login attempt", extra={'custom_data': {"username": "attacker", "ip": "*************"}})
    
    # Example of logging a more complex event
    event_details = {
        "user_id": "user_abc",
        "action": "view_product",
        "product_id": "prod_789",
        "category": "electronics"
    }
    activity_logger.info("Product page viewed", extra={'custom_data': event_details})

    print(f"Test logs written to {log_path}")
    print("Please check the file content.")