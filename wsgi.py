import os
from dotenv import load_dotenv

# Construct the path to the .env file relative to this wsgi.py file
# Assumes .env is in the same directory as wsgi.py (project root)
dotenv_path = os.path.join(os.path.dirname(__file__), '.env')

# Load the .env file if it exists
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path=dotenv_path)
    print("Loaded environment variables from .env") # Optional: confirmation message
else:
    print("Warning: .env file not found at", dotenv_path) # Optional: warning message

# Now import the Flask app object
# <PERSON><PERSON> will look for this 'app' object by default
from app import app

# Optional: Add a check to ensure Flask app is imported
if __name__ == "__main__":
    # This part is typically not run by Gunicorn but can be useful for direct execution checks
    print("wsgi.py executed directly (not recommended for production)")
    # You might add app.run() here for local testing if needed, but it's not standard for wsgi.py
    pass